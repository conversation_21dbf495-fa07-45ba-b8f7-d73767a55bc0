using NafaPlace.Order.API.DTOs;

namespace NafaPlace.Order.API.Services
{
    public class OrderStockService : IOrderStockService
    {
        private readonly IInventoryIntegrationService _inventoryService;
        private readonly ILogger<OrderStockService> _logger;
        private readonly Dictionary<string, OrderReservationInfo> _reservations;
        private readonly object _reservationLock = new object();

        public OrderStockService(IInventoryIntegrationService inventoryService, ILogger<OrderStockService> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
            _reservations = new Dictionary<string, OrderReservationInfo>();
        }

        public async Task<OrderStockValidationResult> ValidateOrderStockAsync(List<CartItemDto> orderItems)
        {
            var result = new OrderStockValidationResult { IsValid = true };

            try
            {
                _logger.LogInformation("Validation des stocks pour {ItemCount} produits", orderItems.Count);

                foreach (var item in orderItems)
                {
                    var validation = await _inventoryService.ValidateStockAvailabilityAsync(item.ProductId, item.Quantity);
                    
                    if (!validation.IsValid)
                    {
                        result.IsValid = false;
                        var error = new StockValidationError
                        {
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            RequestedQuantity = item.Quantity,
                            AvailableQuantity = validation.AvailableStock,
                            ErrorMessage = validation.ErrorMessage ?? "Stock insuffisant",
                            IsCritical = validation.AvailableStock == 0
                        };
                        result.Errors.Add(error);

                        if (error.IsCritical)
                        {
                            result.HasCriticalErrors = true;
                        }
                    }
                }

                if (!result.IsValid)
                {
                    var criticalCount = result.Errors.Count(e => e.IsCritical);
                    result.GeneralErrorMessage = criticalCount > 0 
                        ? $"{criticalCount} produit(s) en rupture de stock, {result.Errors.Count - criticalCount} avec stock insuffisant"
                        : $"{result.Errors.Count} produit(s) avec stock insuffisant";
                }

                _logger.LogInformation("Validation terminée: {IsValid}, {ErrorCount} erreurs", result.IsValid, result.Errors.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la validation des stocks");
                return new OrderStockValidationResult
                {
                    IsValid = false,
                    HasCriticalErrors = true,
                    GeneralErrorMessage = "Erreur technique lors de la validation des stocks"
                };
            }
        }

        public async Task<bool> ReserveStockForOrderAsync(string orderId, string userId, List<CartItemDto> orderItems)
        {
            try
            {
                _logger.LogInformation("Réservation des stocks pour la commande {OrderId}", orderId);

                // Vérifier d'abord que tous les stocks sont disponibles
                var validation = await ValidateOrderStockAsync(orderItems);
                if (!validation.IsValid)
                {
                    _logger.LogWarning("Impossible de réserver les stocks pour la commande {OrderId}: validation échouée", orderId);
                    return false;
                }

                var reservationInfo = new OrderReservationInfo
                {
                    OrderId = orderId,
                    UserId = userId,
                    ReservedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(30), // 30 minutes d'expiration
                    ProductReservations = new List<ProductReservationInfo>()
                };

                var allReservationsSuccessful = true;

                foreach (var item in orderItems)
                {
                    var reservationSuccess = await _inventoryService.ReserveStockAsync(item.ProductId, item.Quantity, userId, orderId);
                    
                    if (reservationSuccess)
                    {
                        reservationInfo.ProductReservations.Add(new ProductReservationInfo
                        {
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            ReservedQuantity = item.Quantity,
                            ReservedAt = DateTime.UtcNow,
                            ExpiresAt = DateTime.UtcNow.AddMinutes(30),
                            IsExpired = false
                        });
                    }
                    else
                    {
                        allReservationsSuccessful = false;
                        _logger.LogError("Échec de la réservation pour le produit {ProductId} dans la commande {OrderId}", item.ProductId, orderId);
                    }
                }

                if (allReservationsSuccessful)
                {
                    lock (_reservationLock)
                    {
                        _reservations[orderId] = reservationInfo;
                    }
                    _logger.LogInformation("Réservations créées avec succès pour la commande {OrderId}", orderId);
                }
                else
                {
                    // En cas d'échec partiel, libérer les réservations réussies
                    await ReleaseStockReservationAsync(orderId, userId, orderItems, "Échec de réservation partielle");
                }

                return allReservationsSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réservation des stocks pour la commande {OrderId}", orderId);
                return false;
            }
        }

        public async Task<bool> ConfirmStockReductionAsync(string orderId, List<CartItemDto> orderItems)
        {
            try
            {
                _logger.LogInformation("Confirmation de la réduction des stocks pour la commande {OrderId}", orderId);

                var success = await _inventoryService.ReduceStockForOrderAsync(orderItems, orderId);
                
                if (success)
                {
                    // Supprimer les réservations car les stocks ont été définitivement réduits
                    lock (_reservationLock)
                    {
                        _reservations.Remove(orderId);
                    }
                    _logger.LogInformation("Stocks réduits avec succès pour la commande {OrderId}", orderId);
                }
                else
                {
                    _logger.LogError("Échec de la réduction des stocks pour la commande {OrderId}", orderId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la confirmation de la réduction des stocks pour la commande {OrderId}", orderId);
                return false;
            }
        }

        public async Task<bool> ReleaseStockReservationAsync(string orderId, string userId, List<CartItemDto> orderItems, string reason)
        {
            try
            {
                _logger.LogInformation("Libération des réservations pour la commande {OrderId}: {Reason}", orderId, reason);

                var allReleasesSuccessful = true;

                foreach (var item in orderItems)
                {
                    var releaseSuccess = await _inventoryService.ReleaseReservationAsync(item.ProductId, item.Quantity, userId, reason);
                    if (!releaseSuccess)
                    {
                        allReleasesSuccessful = false;
                        _logger.LogWarning("Échec de la libération de la réservation pour le produit {ProductId}", item.ProductId);
                    }
                }

                // Supprimer les réservations locales
                lock (_reservationLock)
                {
                    _reservations.Remove(orderId);
                }

                if (allReleasesSuccessful)
                {
                    _logger.LogInformation("Toutes les réservations ont été libérées pour la commande {OrderId}", orderId);
                }

                return allReleasesSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la libération des réservations pour la commande {OrderId}", orderId);
                return false;
            }
        }

        public async Task<int> CleanupExpiredReservationsAsync()
        {
            try
            {
                var expiredCount = 0;
                var expiredOrders = new List<string>();

                lock (_reservationLock)
                {
                    var now = DateTime.UtcNow;
                    foreach (var kvp in _reservations.ToList())
                    {
                        if (kvp.Value.ExpiresAt <= now)
                        {
                            expiredOrders.Add(kvp.Key);
                            _reservations.Remove(kvp.Key);
                            expiredCount++;
                        }
                    }
                }

                // Libérer les réservations expirées dans le service d'inventaire
                foreach (var orderId in expiredOrders)
                {
                    // Note: Nous aurions besoin des détails de la commande pour libérer les réservations
                    // Pour l'instant, on fait appel au service d'inventaire pour nettoyer les réservations expirées
                    await _inventoryService.ReleaseReservationAsync(0, 0, "system", $"Nettoyage automatique - Commande {orderId} expirée");
                }

                if (expiredCount > 0)
                {
                    _logger.LogInformation("Nettoyage terminé: {ExpiredCount} réservations expirées supprimées", expiredCount);
                }

                return expiredCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du nettoyage des réservations expirées");
                return 0;
            }
        }

        public async Task<OrderReservationStatus> GetOrderReservationStatusAsync(string orderId)
        {
            await Task.CompletedTask; // Pour éviter l'avertissement async

            lock (_reservationLock)
            {
                if (_reservations.TryGetValue(orderId, out var reservation))
                {
                    var now = DateTime.UtcNow;
                    var isExpired = reservation.ExpiresAt <= now;

                    return new OrderReservationStatus
                    {
                        OrderId = orderId,
                        HasReservations = true,
                        ProductReservations = reservation.ProductReservations.Select(pr => new ProductReservationInfo
                        {
                            ProductId = pr.ProductId,
                            ProductName = pr.ProductName,
                            ReservedQuantity = pr.ReservedQuantity,
                            ReservedAt = pr.ReservedAt,
                            ExpiresAt = pr.ExpiresAt,
                            IsExpired = pr.ExpiresAt <= now
                        }).ToList(),
                        ReservationExpiry = reservation.ExpiresAt,
                        IsExpired = isExpired
                    };
                }

                return new OrderReservationStatus
                {
                    OrderId = orderId,
                    HasReservations = false
                };
            }
        }

        // Classe interne pour gérer les réservations
        private class OrderReservationInfo
        {
            public string OrderId { get; set; } = string.Empty;
            public string UserId { get; set; } = string.Empty;
            public DateTime ReservedAt { get; set; }
            public DateTime ExpiresAt { get; set; }
            public List<ProductReservationInfo> ProductReservations { get; set; } = new();
        }
    }
}
