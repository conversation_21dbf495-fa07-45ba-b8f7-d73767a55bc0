using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.Application.Interfaces;

public interface IChatNotificationService
{
    // Notifications de chat en temps réel
    Task SendChatNotificationAsync(string userId, ChatNotificationDto notification);
    Task SendMessageNotificationAsync(string userId, int conversationId, string message, string senderName);
    Task SendConversationStatusNotificationAsync(string userId, int conversationId, string status);
    Task SendAgentAssignedNotificationAsync(string userId, int conversationId, string agentName);
    
    // Notifications de groupe
    Task SendGroupNotificationAsync(List<string> userIds, ChatNotificationDto notification);
    Task NotifyConversationParticipantsAsync(int conversationId, ChatNotificationDto notification, string? excludeUserId = null);
    
    // Indicateurs de présence et activité
    Task SendTypingIndicatorAsync(int conversationId, string userId, bool isTyping);
    Task SendUserPresenceUpdateAsync(string userId, UserPresenceStatus status);
    Task NotifyUserJoinedAsync(int conversationId, string userId, string userName);
    Task NotifyUserLeftAsync(int conversationId, string userId, string userName);
    
    // Notifications push et email
    Task SendPushNotificationAsync(string userId, string title, string message, Dictionary<string, string>? data = null);
    Task SendEmailNotificationAsync(string userId, string subject, string htmlContent, string? textContent = null);
    Task SendSMSNotificationAsync(string userId, string message);
    
    // Gestion des préférences de notification
    Task<ChatNotificationPreferencesDto> GetUserNotificationPreferencesAsync(string userId);
    Task<bool> UpdateUserNotificationPreferencesAsync(string userId, ChatNotificationPreferencesDto preferences);
    Task<bool> IsNotificationEnabledAsync(string userId, NotificationType type);
    
    // Notifications système et administratives
    Task SendSystemNotificationAsync(string userId, string message, NotificationPriority priority = NotificationPriority.Normal);
    Task SendMaintenanceNotificationAsync(List<string> userIds, DateTime maintenanceStart, TimeSpan duration);
    Task SendBroadcastNotificationAsync(string message, NotificationPriority priority = NotificationPriority.Normal);
    
    // Historique et statistiques
    Task<List<ChatNotificationDto>> GetUserNotificationHistoryAsync(string userId, int page = 1, int pageSize = 50);
    Task<int> GetUnreadNotificationCountAsync(string userId);
    Task<bool> MarkNotificationAsReadAsync(string userId, int notificationId);
    Task<bool> MarkAllNotificationsAsReadAsync(string userId);
    
    // Configuration et test
    Task<bool> TestNotificationChannelAsync(string userId, NotificationChannel channel);
    Task<Dictionary<string, object>> GetNotificationStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
}

public enum NotificationType
{
    NewMessage,
    ConversationAssigned,
    ConversationClosed,
    AgentJoined,
    AgentLeft,
    TypingIndicator,
    PresenceUpdate,
    SystemMessage,
    Maintenance,
    Broadcast
}

public enum NotificationPriority
{
    Low,
    Normal,
    High,
    Critical
}

public enum NotificationChannel
{
    InApp,
    Push,
    Email,
    SMS,
    WebSocket
}

// UserPresenceStatus moved to ChatDto.cs to avoid duplicates
