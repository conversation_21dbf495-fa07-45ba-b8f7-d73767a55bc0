@page "/login"
@layout EmptyLayout
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Connexion Vendeur</PageTitle>

<div class="auth-page">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-11 col-sm-8 col-md-6 col-lg-4 col-xl-3">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <div class="mb-2">
                                <i class="bi bi-shop seller-portal-icon" style="font-size: 2.5rem; color: var(--primary-color);"></i>
                            </div>
                            <h4 class="fw-bold seller-portal-title">Seller Portal</h4>
                            <p class="text-muted small mb-0">Espace vendeur</p>
                        </div>
                    
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>@errorMessage
                            </div>
                        }

                        <EditForm Model="@loginRequest" OnValidSubmit="HandleLogin">
                            <DataAnnotationsValidator />
                            <ValidationSummary />

                            <div class="mb-3">
                                <label for="email" class="form-label">Adresse email</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <InputText id="email" @bind-Value="loginRequest.Email" class="form-control" placeholder="<EMAIL>" />
                                </div>
                                <ValidationMessage For="@(() => loginRequest.Email)" class="text-danger small" />
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Mot de passe</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <InputText id="password" type="password" @bind-Value="loginRequest.Password" class="form-control" placeholder="Votre mot de passe" />
                                </div>
                                <ValidationMessage For="@(() => loginRequest.Password)" class="text-danger small" />
                            </div>

                            <div class="mb-3 form-check">
                                <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                                <label class="form-check-label" for="rememberMe">Se souvenir de moi</label>
                            </div>

                            <div class="d-grid gap-2 mb-3">
                                <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span class="ms-2">Connexion...</span>
                                    }
                                    else
                                    {
                                        <span><i class="bi bi-box-arrow-in-right me-2"></i>Se connecter</span>
                                    }
                                </button>
                            </div>
                        </EditForm>

                        <div class="text-center mb-3">
                            <a href="/forgot-password" class="text-decoration-none">
                                <i class="bi bi-key me-1"></i>Mot de passe oublié ?
                            </a>
                        </div>

                        <div class="text-center mb-3">
                            <p class="small text-muted mb-2">Vous n'avez pas de compte ?</p>
                            <a href="/register" class="text-decoration-none fw-bold">
                                <i class="bi bi-person-plus me-1"></i>Inscrivez-vous
                            </a>
                        </div>

                        <div class="text-center">
                            <a href="http://localhost:8080" class="text-muted text-decoration-none small">
                                <i class="bi bi-arrow-left me-1"></i>Retour au site principal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginRequest loginRequest = new LoginRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.LoginAsync(loginRequest.Email, loginRequest.Password);
            
            if (result.Success)
            {
                // Vérifier si l'utilisateur a le rôle "Seller"
                var currentUser = await AuthService.GetCurrentUserAsync();
                if (currentUser.Roles.Contains("Seller"))
                {
                    // Rediriger vers le tableau de bord du vendeur
                    NavigationManager.NavigateTo("/dashboard", true);
                }
                else
                {
                    // L'utilisateur n'a pas le rôle "Seller"
                    await AuthService.LogoutAsync();
                    errorMessage = "Vous n'avez pas les autorisations nécessaires pour accéder à l'espace vendeur.";
                }
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la connexion: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
