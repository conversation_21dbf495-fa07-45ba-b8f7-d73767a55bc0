﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using NafaPlace.AdminPortal;
using NafaPlace.AdminPortal.Components;
using NafaPlace.AdminPortal.Models;
using NafaPlace.AdminPortal.Services;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authorization;
using Blazored.LocalStorage;
using Microsoft.JSInterop;
using NafaPlace.Common.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration des API
var apiSettings = builder.Configuration.GetSection("ApiSettings").Get<ApiSettings>();
var baseApiUrl = builder.Configuration["ApiSettings:BaseUrl"] ?? "http://localhost:5000";
Console.WriteLine($"ApiSettings loaded: {apiSettings != null}");
Console.WriteLine($"Base API URL: {baseApiUrl}");
if (apiSettings != null)
{
    Console.WriteLine($"CatalogApiUrl: {apiSettings.CatalogApiUrl}");
    Console.WriteLine($"IdentityApiUrl: {apiSettings.IdentityApiUrl}");
    Console.WriteLine($"InventoryApiUrl: {apiSettings.InventoryApiUrl}");
    Console.WriteLine($"DeliveryApiUrl: {apiSettings.DeliveryApiUrl}");
}

// Ajout du service LocalStorage
builder.Services.AddBlazoredLocalStorage();

// Ajout de HttpClient et HttpClientFactory
builder.Services.AddHttpClient();

// Configuration du client nommé pour l'API Identity
builder.Services.AddHttpClient("Identity", client =>
{
    client.BaseAddress = new Uri($"{baseApiUrl}/");
});

// Configuration du service Gateway centralisé
builder.Services.AddScoped<HttpClient>(sp => new HttpClient());
builder.Services.AddScoped<IGatewayHttpClientService, BlazorWebAssemblyGatewayHttpClientService>();

// Configuration du client HTTP pour l'API Catalog (via Gateway)
builder.Services.AddScoped<CategoryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    return new CategoryService(httpClient);
});

// Configuration du client HTTP pour l'API Identity (via Gateway)
builder.Services.AddScoped<IAuthService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var authStateProvider = sp.GetRequiredService<CustomAuthStateProvider>();

    return new AuthService(httpClient, localStorage, authStateProvider);
});

// Configuration du service de produits (via Gateway)
builder.Services.AddScoped<ProductService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    return new ProductService(httpClient);
});

// Configuration du service d'images (via Gateway)
builder.Services.AddScoped<ImageService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();

    return new ImageService(httpClient, jsRuntime);
});

// Configuration du service de notifications
builder.Services.AddScoped<NotificationService>();

// Configuration du service de coupons (via Gateway)
builder.Services.AddScoped<CouponService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var configuration = sp.GetRequiredService<IConfiguration>();
    var logger = sp.GetRequiredService<ILogger<CouponService>>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();

    return new CouponService(httpClientFactory, configuration, logger, jsRuntime);
});

// Configuration du service de gestion des utilisateurs (via Gateway)
builder.Services.AddScoped<IUserService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new UserService(httpClient, localStorage);
});

// Configuration du service de gestion des commandes (via Gateway)
builder.Services.AddScoped<IOrderService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var authService = sp.GetRequiredService<IAuthService>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();

    return new OrderService(httpClient, authService, localStorage);
});

// Configuration du service de gestion des avis (via Gateway)
builder.Services.AddScoped<IReviewService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new ReviewService(httpClient, localStorage);
});

// Configuration du service de gestion des vendeurs (via Gateway)
builder.Services.AddScoped<SellerService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var logger = sp.GetRequiredService<ILogger<SellerService>>();

    return new SellerService(httpClient, httpClientFactory, logger);
});

// Configuration du service d'inventaire (via Gateway)
builder.Services.AddScoped<InventoryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    return new InventoryService(httpClient);
});

// Configuration du service de livraison (via Gateway)
builder.Services.AddScoped<IDeliveryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var logger = sp.GetRequiredService<ILogger<DeliveryService>>();
    return new DeliveryService(httpClient, logger);
});

// Configuration du service de paiement (via Gateway)
builder.Services.AddScoped<IPaymentService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var logger = sp.GetRequiredService<ILogger<PaymentService>>();
    return new PaymentService(httpClient, logger);
});

// Configuration du service de wishlist (via Gateway)
builder.Services.AddScoped<IWishlistService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri($"{baseApiUrl}/") };
    var logger = sp.GetRequiredService<ILogger<WishlistService>>();
    return new WishlistService(httpClient, logger);
});

// Configuration de l'authentification
builder.Services.AddScoped<CustomAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthStateProvider>());
builder.Services.AddAuthorizationCore(options =>
{
    // Politique pour les administrateurs uniquement
    options.AddPolicy("AdminOnly", policy =>
        policy.RequireRole("Admin"));

    // Politique par défaut : exiger l'authentification
    options.DefaultPolicy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .RequireRole("Admin")
        .Build();
});
builder.Services.AddSingleton<TokenExpirationService>();

// TODO: Service de notifications en temps réel
// builder.Services.AddScoped<INotificationClientService, NotificationClientService>();

var app = builder.Build();

// DÃ©marrer le service de vÃ©rification d'expiration des tokens
var tokenExpirationService = app.Services.GetRequiredService<TokenExpirationService>();
tokenExpirationService.StartTokenExpirationCheck();

await app.RunAsync();
