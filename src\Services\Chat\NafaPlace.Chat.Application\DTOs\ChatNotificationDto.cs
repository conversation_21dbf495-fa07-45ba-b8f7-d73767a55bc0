namespace NafaPlace.Chat.Application.DTOs;

// ChatNotificationDto moved to ChatDto.cs to avoid duplicates

public class ChatNotificationPreferencesDto
{
    public string UserId { get; set; } = string.Empty;
    public bool EnableInAppNotifications { get; set; } = true;
    public bool EnablePushNotifications { get; set; } = true;
    public bool EnableEmailNotifications { get; set; } = true;
    public bool EnableSMSNotifications { get; set; } = false;
    public bool EnableTypingIndicators { get; set; } = true;
    public bool EnablePresenceUpdates { get; set; } = true;
    public Dictionary<NotificationType, bool> NotificationTypeSettings { get; set; } = new();
}
