using NafaPlace.Order.API.DTOs;

namespace NafaPlace.Order.API.Services
{
    public interface IOrderStockService
    {
        /// <summary>
        /// Valide que tous les produits d'une commande ont suffisamment de stock disponible
        /// </summary>
        Task<OrderStockValidationResult> ValidateOrderStockAsync(List<CartItemDto> orderItems);

        /// <summary>
        /// Réserve temporairement les stocks pour une commande en cours de traitement
        /// </summary>
        Task<bool> ReserveStockForOrderAsync(string orderId, string userId, List<CartItemDto> orderItems);

        /// <summary>
        /// Confirme la réservation et réduit définitivement les stocks après paiement réussi
        /// </summary>
        Task<bool> ConfirmStockReductionAsync(string orderId, List<CartItemDto> orderItems);

        /// <summary>
        /// Libère les réservations de stock en cas d'annulation ou d'échec de commande
        /// </summary>
        Task<bool> ReleaseStockReservationAsync(string orderId, string userId, List<CartItemDto> orderItems, string reason);

        /// <summary>
        /// Nettoie les réservations expirées
        /// </summary>
        Task<int> CleanupExpiredReservationsAsync();

        /// <summary>
        /// Obtient le statut des réservations pour une commande
        /// </summary>
        Task<OrderReservationStatus> GetOrderReservationStatusAsync(string orderId);
    }

    public class OrderStockValidationResult
    {
        public bool IsValid { get; set; }
        public List<StockValidationError> Errors { get; set; } = new();
        public bool HasCriticalErrors { get; set; }
        public string? GeneralErrorMessage { get; set; }
    }

    public class StockValidationError
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int RequestedQuantity { get; set; }
        public int AvailableQuantity { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public bool IsCritical { get; set; }
    }

    public class OrderReservationStatus
    {
        public string OrderId { get; set; } = string.Empty;
        public bool HasReservations { get; set; }
        public List<ProductReservationInfo> ProductReservations { get; set; } = new();
        public DateTime? ReservationExpiry { get; set; }
        public bool IsExpired { get; set; }
    }

    public class ProductReservationInfo
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int ReservedQuantity { get; set; }
        public DateTime ReservedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsExpired { get; set; }
    }
}
