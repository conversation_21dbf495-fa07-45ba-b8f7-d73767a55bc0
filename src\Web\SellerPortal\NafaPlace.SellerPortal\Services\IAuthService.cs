using NafaPlace.SellerPortal.Models.Auth;
using NafaPlace.SellerPortal.Models;

namespace NafaPlace.SellerPortal.Services;

public interface IAuthService
{
    event Action? AuthenticationStateChanged;
    Task<AuthResponse> LoginAsync(string email, string password);
    Task<AuthResponse> RegisterAsync(RegisterRequest request);
    Task LogoutAsync();
    Task<UserDto> GetCurrentUserAsync();
    Task<AuthResponse> ForgotPasswordAsync(ForgotPasswordRequest request);
    Task<AuthResponse> ResetPasswordAsync(ResetPasswordRequest request);
}
