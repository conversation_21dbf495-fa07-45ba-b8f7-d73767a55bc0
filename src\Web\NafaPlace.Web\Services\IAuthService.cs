using NafaPlace.Web.Models.Auth;

namespace NafaPlace.Web.Services;

public interface IAuthService
{
    event Action? AuthenticationStateChanged;
    Task<AuthResponse> LoginAsync(LoginRequest request);
    Task<AuthResponse> RegisterAsync(RegisterRequest request);
    Task Logout();
    Task<UserDto> GetCurrentUserAsync();
    
    // Méthodes pour la gestion du profil utilisateur
    Task<UserDto> UpdateProfileAsync(UserDto user);
    Task<bool> ChangePasswordAsync(ChangePasswordRequest request);

    // Méthodes pour la réinitialisation du mot de passe
    Task<AuthResponse> ForgotPasswordAsync(ForgotPasswordRequest request);
    Task<AuthResponse> ResetPasswordAsync(ResetPasswordRequest request);

    // Méthodes pour la gestion des rôles
    Task<List<RoleDto>> GetUserRolesAsync();
    Task<List<RoleDto>> GetAllRolesAsync();
}
