{
  "Routes": [
    // Identity Service Routes
    {
      "DownstreamPathTemplate": "/api/identity/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "identity-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/identity/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "identity"
    },

    // Auth Service Routes (alias for Identity)
    {
      "DownstreamPathTemplate": "/api/auth/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "identity-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/auth/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "auth"
    },

    // Users Service Routes (alias for Identity)
    {
      "DownstreamPathTemplate": "/api/users/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "identity-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/users/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "users"
    },

    // Catalog Service Routes
    {
      "DownstreamPathTemplate": "/api/v1/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "catalog-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/catalog/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "catalog"
    },

    // Cart Service Routes
    {
      "DownstreamPathTemplate": "/api/cart/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "cart-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/cart/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "cart"
    },

    // Order Service Routes
    {
      "DownstreamPathTemplate": "/api/orders/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "order-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/orders/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "orders"
    },

    // Statistics Service Routes (Order Service)
    {
      "DownstreamPathTemplate": "/api/statistics/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "order-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/statistics/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "statistics"
    },

    // Payment Service Routes
    {
      "DownstreamPathTemplate": "/api/payments/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "payment-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/payments/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "payments"
    },

    // Reviews Service Routes
    {
      "DownstreamPathTemplate": "/api/reviews/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "reviews-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/reviews/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "reviews"
    },

    // Reviews Moderation Routes
    {
      "DownstreamPathTemplate": "/api/moderation/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "reviews-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/moderation/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "reviews-moderation"
    },

    // Notifications Service Routes
    {
      "DownstreamPathTemplate": "/api/notifications/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "notifications-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/notifications/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "notifications"
    },

    // Notification Service Routes (webhooks et API v1)
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "notification-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/v1/notification/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "notification-webhooks"
    },

    // Wishlist Service Routes
    {
      "DownstreamPathTemplate": "/api/wishlist/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "wishlist-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/wishlist/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "wishlist"
    },



    // Inventory Service Routes
    {
      "DownstreamPathTemplate": "/api/v1/inventory/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "inventory-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/inventory/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "inventory"
    },

    // Inventory Service Routes v1
    {
      "DownstreamPathTemplate": "/api/v1/inventory/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "inventory-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/v1/inventory/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "inventory-v1"
    },

    // Coupon Service Routes
    {
      "DownstreamPathTemplate": "/api/coupon/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "coupon-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/coupon/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "coupon"
    },

    // Coupon Service Routes (plural)
    {
      "DownstreamPathTemplate": "/api/coupon/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "coupon-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/coupons/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "coupons"
    },

    // Delivery Service Routes
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "delivery-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/delivery/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "delivery"
    },

    // Recommendation Service Routes
    {
      "DownstreamPathTemplate": "/api/recommendations/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "recommendation-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/recommendations/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "recommendations"
    },

    // Chat Service Routes
    {
      "DownstreamPathTemplate": "/api/chat/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "chat-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/chat/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "chat"
    },

    // Analytics Service Routes
    {
      "DownstreamPathTemplate": "/api/analytics/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "analytics-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/analytics/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "analytics"
    },

    // Loyalty Service Routes
    {
      "DownstreamPathTemplate": "/api/loyalty/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "loyalty-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/loyalty/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "loyalty"
    },

    // Localization Service Routes
    {
      "DownstreamPathTemplate": "/api/localization/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localization-api",
          "Port": 80
        }
      ],
      "UpstreamPathTemplate": "/api/localization/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "localization"
    }
  ],

  "GlobalConfiguration": {
    "BaseUrl": "https://nafaplace-test.fly.dev",
    "RequestIdKey": "OcRequestId",
    "AdministrationPath": "/administration"
  }
}
