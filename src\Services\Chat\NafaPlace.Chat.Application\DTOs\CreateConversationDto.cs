namespace NafaPlace.Chat.Application.DTOs;

// DTOs moved to ChatDto.cs to avoid duplicates

public class SatisfactionFeedbackDto
{
    public int ConversationId { get; set; }
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
}
