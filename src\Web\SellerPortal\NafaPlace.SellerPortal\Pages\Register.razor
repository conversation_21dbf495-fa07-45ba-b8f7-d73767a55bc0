@page "/register"
@layout EmptyLayout
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Inscription Vendeur</PageTitle>

<div class="auth-page register-page">
    <div class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="card shadow form-container">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <div class="mb-2">
                                <i class="bi bi-shop seller-portal-icon"></i>
                            </div>
                            <h4 class="fw-bold seller-portal-title">Seller Portal</h4>
                            <h5 class="fw-bold mb-1">Inscription</h5>
                            <p class="text-muted small mb-0">Rejoignez notre communauté de vendeurs</p>
                        </div>

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>@errorMessage
                            </div>
                        }
                    
                        <EditForm Model="@registerRequest" OnValidSubmit="HandleRegister">
                            <DataAnnotationsValidator />
                            <ValidationSummary />

                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <label for="firstName" class="form-label small">Prénom</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                                        <InputText id="firstName" @bind-Value="registerRequest.FirstName" class="form-control" placeholder="Votre prénom" />
                                    </div>
                                    <ValidationMessage For="@(() => registerRequest.FirstName)" class="text-danger small" />
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label for="lastName" class="form-label small">Nom</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                                        <InputText id="lastName" @bind-Value="registerRequest.LastName" class="form-control" placeholder="Votre nom" />
                                    </div>
                                    <ValidationMessage For="@(() => registerRequest.LastName)" class="text-danger small" />
                                </div>
                            </div>

                            <div class="mb-2">
                                <label for="username" class="form-label small">Nom d'utilisateur</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-at"></i></span>
                                    <InputText id="username" @bind-Value="registerRequest.Username" class="form-control" placeholder="Choisissez un nom d'utilisateur" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Username)" class="text-danger small" />
                            </div>

                            <div class="mb-2">
                                <label for="email" class="form-label small">Adresse email</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                    <InputText id="email" @bind-Value="registerRequest.Email" class="form-control" placeholder="<EMAIL>" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Email)" class="text-danger small" />
                            </div>
                        
                            <div class="mb-2">
                                <label for="password" class="form-label small">Mot de passe</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                    <InputText id="password" type="password" @bind-Value="registerRequest.Password" class="form-control" placeholder="Choisissez un mot de passe" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Password)" class="text-danger small" />
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label small">Confirmer le mot de passe</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                    <InputText id="confirmPassword" type="password" @bind-Value="registerRequest.ConfirmPassword" class="form-control" placeholder="Confirmez votre mot de passe" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.ConfirmPassword)" class="text-danger small" />
                            </div>

                            <div class="d-grid gap-2 mb-2">
                                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span class="ms-2">Inscription en cours...</span>
                                    }
                                    else
                                    {
                                        <span><i class="bi bi-person-plus me-2"></i>S'inscrire</span>
                                    }
                                </button>
                            </div>
                        </EditForm>

                        <div class="text-center mb-2">
                            <p class="small text-muted mb-1">Vous avez déjà un compte ?</p>
                            <a href="/login" class="text-decoration-none fw-bold small">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Connectez-vous
                            </a>
                        </div>

                        <div class="text-center">
                            <a href="http://localhost:8080" class="text-muted text-decoration-none small">
                                <i class="bi bi-arrow-left me-1"></i>Retour au site principal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterRequest registerRequest = new RegisterRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    
    protected override async Task OnInitializedAsync()
    {
        // Rediriger vers le tableau de bord si l'utilisateur est déjà connecté
        var currentUser = await AuthService.GetCurrentUserAsync();
        if (currentUser.IsAuthenticated && currentUser.Roles.Contains("Seller"))
        {
            NavigationManager.NavigateTo("/dashboard");
        }
    }
    
    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.RegisterAsync(registerRequest);
            
            if (result.Success)
            {
                // Rediriger vers le tableau de bord du vendeur
                NavigationManager.NavigateTo("/dashboard", true);
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'inscription: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
