# Configuration Sécurisée - NafaPlace

## Variables d'environnement sensibles

Pour éviter d'exposer les mots de passe et clés secrètes dans le code source, nous utilisons des variables d'environnement.

### Configuration initiale

1. **Copiez le fichier d'exemple** :
   ```bash
   cp .env.example .env
   ```

2. **Modifiez le fichier .env** avec vos vraies valeurs :
   ```bash
   # Configuration des emails
   EMAIL_SMTP_PASSWORD=votre-mot-de-passe-application-gmail
   
   # Autres variables sensibles
   JWT_SECRET=votre-cle-jwt-secrete
   DATABASE_PASSWORD=votre-mot-de-passe-db
   ```

### Sécurité

- ✅ Le fichier `.env` est dans `.gitignore` et ne sera jamais commité
- ✅ Les mots de passe ne sont plus en clair dans le code source
- ✅ Chaque développeur peut avoir ses propres credentials
- ✅ Les variables sont chargées automatiquement par Docker Compose

### Pour Gmail

Pour obtenir un mot de passe d'application Gmail :
1. Activez l'authentification à 2 facteurs sur votre compte Google
2. Allez dans "Gérer votre compte Google" > "Sécurité"
3. Sous "Se connecter à Google", cliquez sur "Mots de passe des applications"
4. Générez un nouveau mot de passe d'application pour "Mail"
5. Utilisez ce mot de passe dans la variable `EMAIL_SMTP_PASSWORD`

### En production

En production, utilisez les variables d'environnement du système ou un service de gestion des secrets comme :
- Azure Key Vault
- AWS Secrets Manager
- HashiCorp Vault
- Variables d'environnement du serveur
