@page "/products"
@attribute [AdminOnly]
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using System.ComponentModel.DataAnnotations
@using Microsoft.JSInterop
@using NafaPlace.AdminPortal.Models.Products
@using NafaPlace.AdminPortal.Services
@using InputFile = Microsoft.AspNetCore.Components.Forms.InputFile
@attribute [Authorize]
@inject ProductService ProductService
@inject CategoryService CategoryService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject ImageService ImageService

<h1 class="visually-hidden">Gestion des Produits - NafaPlace Admin</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Produits</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
        <li class="breadcrumb-item active">Produits</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-grid me-1"></i>
                Liste des Produits
            </div>
            <button class="btn btn-primary" @onclick="async () => await OpenAddProductModal()">
                <i class="bi bi-plus-circle me-1"></i> Ajouter un Produit
            </button>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Rechercher un produit..." @bind="_searchTerm" @bind:event="oninput">
                        <button class="btn btn-outline-secondary" type="button" @onclick="OnSearch">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" @bind="_selectedCategoryId" @bind:event="oninput" @onchange="OnCategoryFilterChange">
                        <option value="">Toutes les catégories</option>
                        @foreach (var category in _categories)
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" @bind="_selectedSellerId" @bind:event="oninput" @onchange="OnSellerFilterChange">
                        <option value="">Tous les vendeurs</option>
                        @foreach (var seller in _sellers)
                        {
                            <option value="@seller.Id">@seller.Name</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" @bind="_selectedApprovalStatus" @bind:event="oninput" @onchange="OnApprovalStatusFilterChange">
                        <option value="">Tous les statuts</option>
                        <option value="En attente">En attente</option>
                        <option value="Approuvé">Approuvé</option>
                        <option value="Rejeté">Rejeté</option>
                    </select>
                </div>
            </div>

            @if (_isLoading)
            {
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des produits...</p>
                </div>
            }
            else if (_filteredProducts.Count == 0)
            {
                <div class="alert alert-info" role="alert">
                    Aucun produit trouvé.
                </div>
            }
            else
            {
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th style="width: 80px;">Image</th>
                                <th>Nom</th>
                                <th>Catégorie</th>
                                <th>Vendeur</th>
                                <th>Prix</th>
                                <th>Stock</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in _filteredProducts)
                            {
                                <tr>
                                    <td class="text-center">
                                        <img src="@GetProductImageSrc(product, true)"
                                             alt="Product Image" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;" />
                                    </td>
                                    <td>@product.Name</td>
                                    <td>@product.CategoryName</td>
                                    <td>@product.SellerName</td>
                                    <td>@product.Price.ToString("N0") GNF</td>
                                    <td>@product.Stock</td>
                                    <td>
                                        @if (product.ApprovalStatus == "En attente")
                                        {
                                            <span class="badge bg-warning text-dark">En attente</span>
                                        }
                                        else if (product.ApprovalStatus == "Approuvé")
                                        {
                                            <span class="badge bg-success">Approuvé</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Rejeté</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-primary me-1" @onclick="async () => await OpenEditProductModal(product)">
                                                <i class="bi bi-pencil-fill me-1"></i> Modifier
                                            </button>
                                            <button class="btn btn-sm btn-danger me-1" @onclick="async () => await ConfirmDeleteProduct(product)">
                                                <i class="bi bi-trash-fill me-1"></i> Supprimer
                                            </button>
                                            @if (product.ApprovalStatus == "En attente")
                                            {
                                                <button class="btn btn-sm btn-success me-1" @onclick="async () => await ApproveProduct(product.Id)">
                                                    <i class="bi bi-check-circle-fill me-1"></i> Approuver
                                                </button>
                                                <button class="btn btn-sm btn-warning me-1" @onclick="async () => await OpenRejectProductModal(product.Id)">
                                                    <i class="bi bi-x-circle-fill me-1"></i> Rejeter
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
</div>

<!-- Formulaire de produit -->
<div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalLabel">@(_isNewProduct ? "Ajouter un produit" : "Modifier le produit")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="@_currentProduct" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <!-- Onglets de navigation -->
                    <ul class="nav nav-tabs mb-3" id="productTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                <i class="fas fa-info-circle me-2"></i>Informations générales
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                                <i class="fas fa-cogs me-2"></i>Détails techniques
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab">
                                <i class="fas fa-tags me-2"></i>Prix et stock
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#images" type="button" role="tab">
                                <i class="fas fa-images me-2"></i>Images
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="variants-tab" data-bs-toggle="tab" data-bs-target="#variants" type="button" role="tab">
                                <i class="fas fa-layer-group me-2"></i>Variantes
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="productTabContent">
                        <!-- Onglet Informations générales -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productName" class="form-label">Nom du produit *</label>
                                        <InputText id="productName" class="form-control" @bind-Value="_currentProduct.Name" />
                                        <ValidationMessage For="@(() => _currentProduct.Name)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productBrand" class="form-label">Marque</label>
                                        <InputText id="productBrand" class="form-control" @bind-Value="_currentProduct.Brand" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productModel" class="form-label">Modèle</label>
                                        <InputText id="productModel" class="form-control" @bind-Value="_currentProduct.Model" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productSku" class="form-label">Code produit (SKU)</label>
                                        <InputText id="productSku" class="form-control" @bind-Value="_currentProduct.Sku" placeholder="Ex: PROD-001" />
                                        <div class="form-text">Code unique pour identifier le produit</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productCategory" class="form-label">Catégorie *</label>
                                        <InputSelect id="productCategory" class="form-select" @bind-Value="_currentProduct.CategoryId">
                                            <option value="">Sélectionner une catégorie</option>
                                            @foreach (var category in _categories)
                                            {
                                                <option value="@category.Id">@category.Name</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="@(() => _currentProduct.CategoryId)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productSeller" class="form-label">Vendeur *</label>
                                        <InputSelect id="productSeller" class="form-select" @bind-Value="_currentProduct.SellerId">
                                            <option value="">Sélectionner un vendeur</option>
                                            @foreach (var seller in _sellers)
                                            {
                                                <option value="@seller.Id">@seller.Name</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="@(() => _currentProduct.SellerId)" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productDescription" class="form-label">Description *</label>
                                        <InputTextArea id="productDescription" class="form-control" @bind-Value="_currentProduct.Description" rows="4" />
                                        <ValidationMessage For="@(() => _currentProduct.Description)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productFeatures" class="form-label">Caractéristiques principales</label>
                                        <InputTextArea id="productFeatures" class="form-control" @bind-Value="_currentProduct.Features" rows="3" placeholder="Listez les principales caractéristiques du produit..." />
                                        <div class="form-text">Séparez chaque caractéristique par une nouvelle ligne</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productTags" class="form-label">Tags</label>
                                        <InputText id="productTags" class="form-control" @bind-Value="_currentProduct.Tags" placeholder="électronique, smartphone, android" />
                                        <div class="form-text">Séparez les tags par des virgules</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productApprovalStatus" class="form-label">Statut d'approbation</label>
                                        <InputSelect id="productApprovalStatus" class="form-select" @bind-Value="_currentProduct.ApprovalStatus">
                                            <option value="En attente">En attente</option>
                                            <option value="Approuvé">Approuvé</option>
                                            <option value="Rejeté">Rejeté</option>
                                        </InputSelect>
                                    </div>

                                    @if (_currentProduct.ApprovalStatus == "Rejeté")
                                    {
                                        <div class="mb-3">
                                            <label for="productRejectionReason" class="form-label">Raison du rejet</label>
                                            <InputTextArea id="productRejectionReason" class="form-control" @bind-Value="_currentProduct.RejectionReason" rows="3" placeholder="Expliquez pourquoi ce produit a été rejeté..." />
                                        </div>
                                    }

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <InputCheckbox id="productIsActive" class="form-check-input" @bind-Value="_currentProduct.IsActive" />
                                                <label class="form-check-label" for="productIsActive">Produit actif</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <InputCheckbox id="productIsFeatured" class="form-check-input" @bind-Value="_currentProduct.IsFeatured" />
                                                <label class="form-check-label" for="productIsFeatured">Produit vedette</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Détails techniques -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productColor" class="form-label">Couleur</label>
                                        <InputText id="productColor" class="form-control" @bind-Value="_currentProduct.Color" placeholder="Ex: Rouge, Bleu, Multicolore" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productSize" class="form-label">Taille</label>
                                        <InputText id="productSize" class="form-control" @bind-Value="_currentProduct.Size" placeholder="Ex: S, M, L, XL, 42, 38" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productMaterial" class="form-label">Matériau</label>
                                        <InputText id="productMaterial" class="form-control" @bind-Value="_currentProduct.Material" placeholder="Ex: Coton, Plastique, Métal" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productManufacturer" class="form-label">Fabricant</label>
                                        <InputText id="productManufacturer" class="form-control" @bind-Value="_currentProduct.Manufacturer" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productCountryOfOrigin" class="form-label">Pays d'origine</label>
                                        <InputText id="productCountryOfOrigin" class="form-control" @bind-Value="_currentProduct.CountryOfOrigin" placeholder="Ex: Guinée, France, Chine" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productCondition" class="form-label">État du produit</label>
                                        <InputSelect id="productCondition" class="form-select" @bind-Value="_currentProduct.Condition">
                                            <option value="Neuf">Neuf</option>
                                            <option value="Comme neuf">Comme neuf</option>
                                            <option value="Très bon état">Très bon état</option>
                                            <option value="Bon état">Bon état</option>
                                            <option value="État correct">État correct</option>
                                            <option value="Reconditionné">Reconditionné</option>
                                        </InputSelect>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productWeight" class="form-label">Poids (kg)</label>
                                        <InputNumber id="productWeight" class="form-control" @bind-Value="_currentProduct.Weight" step="0.01" />
                                        <div class="form-text">Poids en kilogrammes</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productDimensions" class="form-label">Dimensions</label>
                                        <InputText id="productDimensions" class="form-control" @bind-Value="_currentProduct.Dimensions" placeholder="Ex: 20x15x5 cm" />
                                        <div class="form-text">Format: Longueur x Largeur x Hauteur</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productWarranty" class="form-label">Garantie (mois)</label>
                                        <InputNumber id="productWarranty" class="form-control" @bind-Value="_currentProduct.WarrantyMonths" />
                                        <div class="form-text">Durée de garantie en mois</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <InputCheckbox id="productRequiresShipping" class="form-check-input" @bind-Value="_currentProduct.RequiresShipping" />
                                                <label class="form-check-label" for="productRequiresShipping">Nécessite livraison</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <InputCheckbox id="productIsDigital" class="form-check-input" @bind-Value="_currentProduct.IsDigital" />
                                                <label class="form-check-label" for="productIsDigital">Produit numérique</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Prix et stock -->
                        <div class="tab-pane fade" id="pricing" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productPrice" class="form-label">Prix *</label>
                                        <div class="input-group">
                                            <InputNumber id="productPrice" class="form-control" @bind-Value="_currentProduct.Price" step="0.01" />
                                            <span class="input-group-text">GNF</span>
                                        </div>
                                        <ValidationMessage For="@(() => _currentProduct.Price)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="productCompareAtPrice" class="form-label">Prix de comparaison</label>
                                        <div class="input-group">
                                            <InputNumber id="productCompareAtPrice" class="form-control" @bind-Value="_currentProduct.CompareAtPrice" step="0.01" />
                                            <span class="input-group-text">GNF</span>
                                        </div>
                                        <div class="form-text">Prix barré pour montrer une réduction</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productStock" class="form-label">Stock *</label>
                                        <InputNumber id="productStock" class="form-control" @bind-Value="_currentProduct.Stock" />
                                        <ValidationMessage For="@(() => _currentProduct.Stock)" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productMinOrderQuantity" class="form-label">Quantité minimum de commande</label>
                                        <InputNumber id="productMinOrderQuantity" class="form-control" @bind-Value="_currentProduct.MinOrderQuantity" />
                                        <div class="form-text">Quantité minimum par commande</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productMaxOrderQuantity" class="form-label">Quantité maximum de commande</label>
                                        <InputNumber id="productMaxOrderQuantity" class="form-control" @bind-Value="_currentProduct.MaxOrderQuantity" />
                                        <div class="form-text">Quantité maximum par commande (optionnel)</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="productAvailableFrom" class="form-label">Disponible à partir du</label>
                                        <InputDate id="productAvailableFrom" class="form-control" @bind-Value="_currentProduct.AvailableFrom" />
                                        <div class="form-text">Date de disponibilité du produit</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Images -->
                        <div class="tab-pane fade" id="images" role="tabpanel">
                            <div class="mb-3">
                                <label class="form-label">Images du produit</label>
                                <div class="d-flex flex-wrap gap-2 mb-2">
                                    @if (_currentProduct.Images != null && _currentProduct.Images.Any())
                                    {
                                        @foreach (var image in _currentProduct.Images)
                                        {
                                            <div class="position-relative" style="width: 100px; height: 100px;">
                                                <img src="@ProductService.GetImageUrl(image, true)"
                                                     alt="Product Image" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;" />
                                                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0" @onclick="() => RemoveImage(image)">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                                @if (!image.IsMain)
                                                {
                                                    <button type="button" class="btn btn-sm btn-primary position-absolute bottom-0 start-0" @onclick="() => SetMainImage(image)">
                                                        <i class="bi bi-star"></i>
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success position-absolute bottom-0 start-0">
                                                        <i class="bi bi-star-fill"></i>
                                                    </span>
                                                }
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="alert alert-warning">
                                            Aucune image pour ce produit.
                                        </div>
                                    }
                                </div>
                                
                                <InputFile OnChange="OnFileSelected" class="form-control" accept="image/*" multiple />
                                @if (_isUploading)
                                {
                                    <div class="mt-2">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                                        </div>
                                        <small class="text-muted">Téléchargement en cours...</small>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Onglet Variantes -->
                        <div class="tab-pane fade" id="variants" role="tabpanel">
                            <div class="p-3">
                                <h5>Variantes du produit</h5>
                                @if (_currentProduct.Variants != null && _currentProduct.Variants.Any())
                                {
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>SKU</th>
                                                <th>Prix</th>
                                                <th>Stock</th>
                                                <th>Couleur</th>
                                                <th>Taille</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var variant in _currentProduct.Variants)
                                            {
                                                <tr>
                                                    <td>@variant.Name</td>
                                                    <td>@variant.Sku</td>
                                                    <td>@variant.Price.ToString("N0") GNF</td>
                                                    <td>@variant.Stock</td>
                                                    <td>@variant.Color</td>
                                                    <td>@variant.Size</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger" @onclick="() => RemoveVariant(variant.Id)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                }
                                else
                                {
                                    <p>Aucune variante pour ce produit.</p>
                                }

                                <hr />

                                <h5>Ajouter une nouvelle variante</h5>
                                <div class="row">
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Nom</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Name" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">SKU</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Sku" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Prix</label>
                                        <InputNumber class="form-control" @bind-Value="_newVariant.Price" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Stock</label>
                                        <InputNumber class="form-control" @bind-Value="_newVariant.Stock" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Couleur</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Color" />
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">Taille</label>
                                        <InputText class="form-control" @bind-Value="_newVariant.Size" />
                                    </div>
                                    <div class="col-md-1 mb-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-success" @onclick="AddVariant">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            @(_isNewProduct ? "Ajouter" : "Modifier")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteConfirmationModal" tabindex="-1" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmationModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer le produit <strong>@(_productToDelete?.Name)</strong> ?
                <br />
                Cette action est irréversible.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" @onclick="DeleteProduct">Supprimer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de rejet de produit -->
<div class="modal fade" id="rejectProductModal" tabindex="-1" aria-labelledby="rejectProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectProductModalLabel">Motif de rejet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label">Raison du rejet</label>
                    <textarea id="rejectionReason" class="form-control" @bind="_rejectionReason" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" @onclick="RejectProduct">Rejeter</button>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ProductDto> _products = new();
    private List<ProductDto> _filteredProducts = new();
    private List<NafaPlace.AdminPortal.Models.Products.CategoryDto> _categories = new();
    private List<SellerDto> _sellers = new();
    private ProductDto _currentProduct = new();
    private ProductDto _productToDelete = new();
    private bool _isLoading = true;
    private bool _isNewProduct = true;
    private bool _isUploading = false;
    private string _searchTerm = "";
    private string _selectedCategoryId = "";
    private string _selectedSellerId = "";
    private string _selectedApprovalStatus = "";
    private string _rejectionReason = "";
    private int _productIdToReject;
    private ProductVariantDto _newVariant = new();
    private IJSObjectReference? _module;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }


    private async Task LoadData()
    {
        try
        {
            _isLoading = true;
            
            // Charger les produits
            _products = await ProductService.GetProductsAsync();
            _filteredProducts = new List<ProductDto>(_products);
            
            // Charger les catégories
            _categories = await ProductService.GetCategoriesAsync();
            
            // Charger les vendeurs
            _sellers = await ProductService.GetSellersAsync();
            
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur lors du chargement des données: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void ApplyFilters()
    {
        _filteredProducts = _products;
        
        // Filtrer par terme de recherche
        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            _filteredProducts = _filteredProducts.Where(p => 
                p.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) || 
                p.Description.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
        }
        
        // Filtrer par catégorie
        if (!string.IsNullOrWhiteSpace(_selectedCategoryId) && int.TryParse(_selectedCategoryId, out int categoryId))
        {
            _filteredProducts = _filteredProducts.Where(p => p.CategoryId == categoryId).ToList();
        }
        
        // Filtrer par vendeur
        if (!string.IsNullOrWhiteSpace(_selectedSellerId) && int.TryParse(_selectedSellerId, out int sellerId) && sellerId > 0)
        {
            _filteredProducts = _filteredProducts.Where(p => p.SellerId == sellerId).ToList();
        }
        
        // Filtrer par statut d'approbation
        if (!string.IsNullOrWhiteSpace(_selectedApprovalStatus))
        {
            _filteredProducts = _filteredProducts.Where(p => p.ApprovalStatus == _selectedApprovalStatus).ToList();
        }
    }

    private void OnSearch()
    {
        ApplyFilters();
    }

    private void OnCategoryFilterChange()
    {
        ApplyFilters();
    }

    private void OnSellerFilterChange()
    {
        ApplyFilters();
    }

    private void OnApprovalStatusFilterChange()
    {
        ApplyFilters();
    }

    private async Task OpenAddProductModal()
    {
        _isNewProduct = true;
        _currentProduct = new ProductDto
        {
            IsActive = true,
            IsApproved = false,
            ApprovalStatus = "En attente",
            Images = new List<ProductImageDto>(),
            Variants = new List<ProductVariantDto>()
        };
        _newVariant = new ProductVariantDto();
        
        await JSRuntime.InvokeVoidAsync("showModal", "productModal");
    }

    private async Task OpenEditProductModal(ProductDto product)
    {
        try
        {
            // Récupérer le produit complet depuis l'API pour s'assurer d'avoir toutes les données à jour
            var freshProduct = await ProductService.GetProductAsync(product.Id);
            
            if (freshProduct != null)
            {
                _isNewProduct = false;
                _currentProduct = freshProduct;
                
                await JSRuntime.InvokeVoidAsync("showModal", "productModal");
            }
            else
            {
                await NotificationService.ShowErrorAsync("Impossible de récupérer les détails du produit.");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur lors de l'ouverture du produit: {ex.Message}");
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            if (_currentProduct.SellerId == null || _currentProduct.SellerId == 0)
            {
                await NotificationService.ShowErrorAsync("Veuillez sélectionner un vendeur.");
                return;
            }

            bool success;
            
            if (_isNewProduct)
            {
                success = await ProductService.CreateProductAsync(_currentProduct);
                
                if (success)
                {
                    var newProductId = ProductService.GetLastCreatedProductId();
                    
                    if (_currentProduct.Images != null && _currentProduct.Images.Any())
                    {
                        if (newProductId > 0)
                        {
                            foreach (var tempImage in _currentProduct.Images.ToList())
                            {
                                await ImageService.UploadProductImageAsync(newProductId, tempImage.ImageUrl, tempImage.IsMain);
                            }
                        }
                    }
                    
                    await NotificationService.ShowSuccessAsync("Produit ajouté avec succès.");
                    await JSRuntime.InvokeVoidAsync("hideModal", "productModal");
                    await LoadData();
                }
                else
                {
                    await NotificationService.ShowErrorAsync("Erreur lors de l'ajout du produit.");
                }
            }
            else
            {
                success = await ProductService.UpdateProductAsync(_currentProduct);
                
                if (success)
                {
                    await NotificationService.ShowSuccessAsync("Produit mis à jour avec succès.");
                    await JSRuntime.InvokeVoidAsync("hideModal", "productModal");
                    await LoadData();
                }
                else
                {
                    await NotificationService.ShowErrorAsync("Erreur lors de la mise à jour du produit.");
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur: {ex.Message}");
        }
    }

    private async Task ConfirmDeleteProduct(ProductDto product)
    {
        _productToDelete = product;
        await JSRuntime.InvokeVoidAsync("showModal", "deleteConfirmationModal");
    }

    private async Task DeleteProduct()
    {
        try
        {
            if (_productToDelete != null)
            {
                var success = await ProductService.DeleteProductAsync(_productToDelete.Id);
                
                if (success)
                {
                    await NotificationService.ShowSuccessAsync("Produit supprimé avec succès.");
                    await JSRuntime.InvokeVoidAsync("hideModal", "deleteConfirmationModal");
                    await LoadData();
                }
                else
                {
                    await NotificationService.ShowErrorAsync("Erreur lors de la suppression du produit.");
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur: {ex.Message}");
        }
    }

    private async Task OpenRejectProductModal(int productId)
    {
        _productIdToReject = productId;
        _rejectionReason = "";
        await JSRuntime.InvokeVoidAsync("showModal", "rejectProductModal");
    }

    private async Task RejectProduct()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(_rejectionReason))
            {
                await NotificationService.ShowWarningAsync("Veuillez indiquer une raison de rejet.");
                return;
            }
            
            var success = await ProductService.RejectProductAsync(_productIdToReject, _rejectionReason);
            
            if (success)
            {
                await NotificationService.ShowSuccessAsync("Produit rejeté avec succès.");
                await JSRuntime.InvokeVoidAsync("hideModal", "rejectProductModal");
                await LoadData();
            }
            else
            {
                await NotificationService.ShowErrorAsync("Erreur lors du rejet du produit.");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur: {ex.Message}");
        }
    }

    private async Task ApproveProduct(int productId)
    {
        try
        {
            var success = await ProductService.ApproveProductAsync(productId);
            
            if (success)
            {
                await NotificationService.ShowSuccessAsync("Produit approuvé avec succès.");
                await LoadData();
            }
            else
            {
                await NotificationService.ShowErrorAsync("Erreur lors de l'approbation du produit.");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur: {ex.Message}");
        }
    }

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            _isUploading = true;
            var maxFileSize = 5 * 1024 * 1024; // 5 MB
            var files = e.GetMultipleFiles();

            foreach (var file in files)
            {
                if (file.Size > maxFileSize)
                {
                    await NotificationService.ShowErrorAsync($"Le fichier {file.Name} est trop volumineux. Taille maximale: 5 Mo.");
                    continue;
                }

                var base64Data = await ImageService.ConvertFileToBase64(file);

                if (string.IsNullOrEmpty(base64Data))
                {
                    await NotificationService.ShowErrorAsync($"Erreur lors de la conversion de l'image {file.Name}.");
                    continue;
                }

                if (_currentProduct.Id <= 0)
                {
                    if (_currentProduct.Images == null)
                    {
                        _currentProduct.Images = new List<ProductImageDto>();
                    }

                    var tempImage = new ProductImageDto
                    {
                        Id = -(_currentProduct.Images.Count + 1),
                        ImageUrl = base64Data.StartsWith("data:image") ? base64Data : $"data:image/jpeg;base64,{base64Data}",
                        FileName = file.Name,
                        IsMain = _currentProduct.Images.Count == 0
                    };

                    _currentProduct.Images.Add(tempImage);
                    await NotificationService.ShowSuccessAsync($"Image {file.Name} ajoutée temporairement.");
                }
                else
                {
                    var imageResult = await ImageService.UploadProductImageAsync(_currentProduct.Id, base64Data, _currentProduct.Images.Count == 0);

                    if (string.IsNullOrEmpty(imageResult))
                    {
                        await NotificationService.ShowErrorAsync($"Erreur lors de l'upload de l'image {file.Name}.");
                        continue;
                    }

                    if (_currentProduct.Images == null)
                    {
                        _currentProduct.Images = new List<ProductImageDto>();
                    }

                    var newImage = new ProductImageDto
                    {
                        Id = await ImageService.GetLastUploadedImageId(),
                        ImageUrl = imageResult,
                        FileName = file.Name,
                        ProductId = _currentProduct.Id,
                        IsMain = _currentProduct.Images.Count == 0
                    };

                    _currentProduct.Images.Add(newImage);
                    await NotificationService.ShowSuccessAsync($"Image {file.Name} ajoutée avec succès.");
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur lors de l'upload des images: {ex.Message}");
        }
        finally
        {
            _isUploading = false;
            StateHasChanged();
        }
    }

    private async Task RemoveImage(ProductImageDto image)
    {
        try
        {
            // Si c'est l'image principale, vérifier s'il y a d'autres images
            if (image.IsMain && _currentProduct.Images.Count > 1)
            {
                // Définir la prochaine image comme principale
                var nextImage = _currentProduct.Images.FirstOrDefault(i => i.Id != image.Id);
                if (nextImage != null)
                {
                    nextImage.IsMain = true;
                }
            }
            
            // Vérifier que l'image a un ID valide
            if (image.Id <= 0 || _currentProduct.Id <= 0)
            {
                await NotificationService.ShowErrorAsync("Impossible de supprimer une image qui n'a pas encore été enregistrée.");
                return;
            }
            
            // Supprimer l'image du serveur avec la nouvelle méthode
            var success = await ImageService.DeleteProductImageAsync(_currentProduct.Id, image.Id);
            
            if (!success)
            {
                await NotificationService.ShowErrorAsync("Erreur lors de la suppression de l'image sur le serveur.");
                return;
            }
            
            // Supprimer l'image de la liste
            _currentProduct.Images.Remove(image);
            
            await NotificationService.ShowSuccessAsync("Image supprimée avec succès.");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur lors de la suppression de l'image: {ex.Message}");
        }
    }

    private async Task SetMainImage(ProductImageDto image)
    {
        try
        {
            // Vérifier que l'image a un ID valide
            if (image.Id <= 0 || _currentProduct.Id <= 0)
            {
                await NotificationService.ShowErrorAsync("Impossible de définir comme principale une image qui n'a pas encore été enregistrée.");
                return;
            }
            
            // Appeler l'API pour définir l'image comme principale
            var success = await ImageService.SetMainImageAsync(_currentProduct.Id, image.Id);
            
            if (!success)
            {
                await NotificationService.ShowErrorAsync("Erreur lors de la définition de l'image principale sur le serveur.");
                return;
            }
            
            // Désactiver l'image principale actuelle
            foreach (var img in _currentProduct.Images)
            {
                img.IsMain = false;
            }
            
            // Définir la nouvelle image principale
            image.IsMain = true;
            
            await NotificationService.ShowSuccessAsync("Image principale définie avec succès.");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"Erreur lors de la définition de l'image principale: {ex.Message}");
        }
    }

    private string GetProductImageSrc(ProductDto product, bool thumbnail = false)
    {
        try
        {
            if (product.Images != null && product.Images.Any())
            {
                var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                if (mainImage != null)
                {
                    return ProductService.GetImageUrl(mainImage, thumbnail);
                }
                
                var firstImage = product.Images.FirstOrDefault();
                if (firstImage != null)
                {
                    return ProductService.GetImageUrl(firstImage, thumbnail);
                }
            }
            
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }

    private void AddVariant()
    {
        if (!string.IsNullOrWhiteSpace(_newVariant.Name) && !string.IsNullOrWhiteSpace(_newVariant.Sku))
        {
            _currentProduct.Variants.Add(new ProductVariantDto
            {
                Id = 0, // Nouveau variant
                Name = _newVariant.Name,
                Sku = _newVariant.Sku,
                Price = _newVariant.Price,
                Stock = _newVariant.Stock,
                Color = _newVariant.Color ?? string.Empty,
                Size = _newVariant.Size ?? string.Empty
            });
            _newVariant = new ProductVariantDto(); // Reset du formulaire
        }
    }

    private void RemoveVariant(int variantId)
    {
        var variant = _currentProduct.Variants.FirstOrDefault(v => v.Id == variantId);
        if (variant != null)
        {
            _currentProduct.Variants.Remove(variant);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_module is not null)
        {
            await _module.DisposeAsync();
        }
    }
}
