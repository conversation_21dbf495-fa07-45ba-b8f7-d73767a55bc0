/* Styles spécifiques pour les pages d'authentification */

.auth-body {
    background: linear-gradient(135deg, #003366 0%, #0066cc 50%, #003366 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

.auth-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
}

/* Pages avec fond blanc (login, register, forgot-password, reset-password) */
.auth-page.login-page,
.auth-page.register-page,
.auth-page.forgot-password-page,
.auth-page.reset-password-page {
    background: #f8f9fa;
    min-height: 100vh;
}

/* Supprimer le fond bleu et les effets pour ces pages spécifiques */
.auth-page.login-page::before,
.auth-page.register-page::before,
.auth-page.forgot-password-page::before,
.auth-page.reset-password-page::before {
    display: none;
}

.form-container {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.form-container .card-body {
    padding: 3rem 2.5rem;
}

.form-container h3 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-container .text-muted {
    color: #6c757d !important;
    font-size: 0.95rem;
}

.form-container .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-container .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-container .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-container .input-group-text {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 10px 0 0 10px;
    color: #6c757d;
}

.form-container .input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

.form-container .input-group:focus-within .input-group-text {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

/* Boutons orange pour toutes les pages d'authentification */
.auth-page .btn-primary,
.form-container .btn-primary,
.forgot-password-page .btn-primary,
.reset-password-page .btn-primary {
    background: #f27a1a !important;
    background-color: #f27a1a !important;
    background-image: none !important;
    border-color: #f27a1a !important;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white !important;
}

.auth-page .btn-primary:hover,
.form-container .btn-primary:hover,
.forgot-password-page .btn-primary:hover,
.reset-password-page .btn-primary:hover {
    background: #e06500 !important;
    background-color: #e06500 !important;
    background-image: none !important;
    border-color: #e06500 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(242, 122, 26, 0.3);
    color: white !important;
}

.auth-page .btn-primary:disabled,
.form-container .btn-primary:disabled,
.forgot-password-page .btn-primary:disabled,
.reset-password-page .btn-primary:disabled {
    background: #f27a1a !important;
    background-color: #f27a1a !important;
    background-image: none !important;
    border-color: #f27a1a !important;
    opacity: 0.7;
    transform: none;
    box-shadow: none;
    color: white !important;
}

.form-container .alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.form-container .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-left: 4px solid #dc3545;
}

.form-container .alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border-left: 4px solid #198754;
}

.form-container a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.form-container a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .auth-page {
        padding: 1rem;
    }
    
    .form-container .card-body {
        padding: 2rem 1.5rem;
    }
}

/* Animation pour les éléments */
.form-container {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Style pour les messages de validation */
.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Style pour le spinner de chargement */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
