@page "/auth/register"
@layout AuthLayout
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>NafaPlace - Inscription</PageTitle>

<div class="auth-page">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-11 col-sm-8 col-md-6 col-lg-4 col-xl-3">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-3">
                    <div class="text-center mb-3">
                        <img src="/images/nafaplace-gradient-modern.svg" alt="NafaPlace" height="50" class="mb-2">
                        <h4 class="fw-bold">Créer un compte</h4>
                        <p class="text-muted small mb-0">Rejoignez la communauté <PERSON>lace</p>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            @errorMessage
                        </div>
                    }

                    @if (registrationSuccess)
                    {
                        <div class="alert alert-success" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            Votre compte a été créé avec succès! Vous allez être redirigé vers la page de connexion...
                        </div>
                    }
                    else
                    {
                        <EditForm Model="registerRequest" OnValidSubmit="HandleRegister">
                            <DataAnnotationsValidator />

                            <div class="row">
                                <div class="col-md-6 mb-1">
                                    <label for="firstName" class="form-label small mb-1">Prénom</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                                        <InputText id="firstName" class="form-control" @bind-Value="registerRequest.FirstName" placeholder="Votre prénom" />
                                    </div>
                                    <ValidationMessage For="@(() => registerRequest.FirstName)" class="text-danger small" />
                                </div>

                                <div class="col-md-6 mb-1">
                                    <label for="lastName" class="form-label small mb-1">Nom</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                                        <InputText id="lastName" class="form-control" @bind-Value="registerRequest.LastName" placeholder="Votre nom" />
                                    </div>
                                    <ValidationMessage For="@(() => registerRequest.LastName)" class="text-danger small" />
                                </div>
                            </div>

                            <div class="mb-1">
                                <label for="email" class="form-label small mb-1">Email</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                    <InputText id="email" class="form-control" @bind-Value="registerRequest.Email" placeholder="<EMAIL>" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Email)" class="text-danger small" />
                            </div>

                            <div class="mb-1">
                                <label for="phoneNumber" class="form-label small mb-1">Numéro de téléphone</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-phone"></i></span>
                                    <InputText id="phoneNumber" class="form-control" @bind-Value="registerRequest.PhoneNumber" placeholder="+221 xx xxx xx xx" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.PhoneNumber)" class="text-danger small" />
                                <small class="text-muted">Nous utiliserons ce numéro pour les notifications de livraison</small>
                            </div>

                            <div class="mb-1">
                                <label for="username" class="form-label small mb-1">Nom d'utilisateur</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                    <InputText id="username" class="form-control" @bind-Value="registerRequest.Username" placeholder="Choisissez un nom d'utilisateur" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Username)" class="text-danger small" />
                            </div>

                            <div class="mb-1">
                                <label for="password" class="form-label small mb-1">Mot de passe</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                    <InputText id="password" type="password" class="form-control" @bind-Value="registerRequest.Password" placeholder="Créez un mot de passe sécurisé" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.Password)" class="text-danger small" />
                                <small class="text-muted">Minimum 8 caractères, incluant lettres, chiffres et caractères spéciaux</small>
                            </div>

                            <div class="mb-2">
                                <label for="confirmPassword" class="form-label small mb-1">Confirmer le mot de passe</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                    <InputText id="confirmPassword" type="password" class="form-control" @bind-Value="registerRequest.ConfirmPassword" placeholder="Confirmez votre mot de passe" />
                                </div>
                                <ValidationMessage For="@(() => registerRequest.ConfirmPassword)" class="text-danger small" />
                            </div>

                            <div class="mb-2 form-check">
                                <InputCheckbox id="termsAccepted" class="form-check-input" @bind-Value="registerRequest.TermsAccepted" />
                                <label class="form-check-label small" for="termsAccepted">
                                    J'accepte les <a href="/terms" target="_blank">conditions générales</a> et la <a href="/privacy" target="_blank">politique de confidentialité</a>
                                </label>
                                <ValidationMessage For="@(() => registerRequest.TermsAccepted)" class="d-block text-danger small" />
                            </div>

                            <button type="submit" class="btn btn-primary w-100 py-1 mb-2" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span>Création en cours...</span>
                                }
                                else
                                {
                                    <i class="bi bi-person-plus-fill me-2"></i>
                                    <span>Créer mon compte</span>
                                }
                            </button>
                        </EditForm>
                    }

                    <div class="text-center mt-4">
                        <p class="mb-0">Vous avez déjà un compte?</p>
                        <a href="/auth/login" class="btn btn-outline-primary mt-2 w-100">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Se connecter
                        </a>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterRequest registerRequest = new RegisterRequest();
    private bool isLoading = false;
    private bool registrationSuccess = false;
    private string errorMessage = string.Empty;

    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            Console.WriteLine("Début du processus d'inscription");

            if (registerRequest.Password != registerRequest.ConfirmPassword)
            {
                errorMessage = "Les mots de passe ne correspondent pas.";
                Console.WriteLine("Erreur: Les mots de passe ne correspondent pas");
                return;
            }

            Console.WriteLine("Appel du service d'inscription");
            var result = await AuthService.RegisterAsync(registerRequest);
            Console.WriteLine($"Résultat de l'inscription: {(result != null ? "Succès" : "Échec")}");

            if (result != null)
            {
                registrationSuccess = true;
                Console.WriteLine("Inscription réussie, attente avant redirection...");
                
                // Attendre 2 secondes avant la redirection
                await Task.Delay(2000);
                
                Console.WriteLine("Redirection vers la page de connexion");
                // Utiliser NavigateTo avec forceLoad=true pour forcer un rechargement complet de la page
                // et éviter toute interférence avec l'état d'authentification
                NavigationManager.NavigateTo("/auth/login", forceLoad: true);
                return; // Ajout d'un return explicite pour éviter toute exécution de code après la redirection
            }
            else
            {
                errorMessage = "Échec de l'inscription. Veuillez réessayer.";
                Console.WriteLine("Erreur: Échec de l'inscription");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite lors de l'inscription: {ex.Message}";
            Console.WriteLine($"Exception lors de l'inscription: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
