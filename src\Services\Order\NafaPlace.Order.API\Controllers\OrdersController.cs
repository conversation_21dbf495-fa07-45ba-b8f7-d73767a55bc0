using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Order.Application;
using NafaPlace.Order.API.Services;
using NafaPlace.Order.API.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using System.Collections.Generic;
using System.Text;
using System.Net.Http;

namespace NafaPlace.Order.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrdersController : ControllerBase
    {
        private readonly IOrderRepository _repository;
        private readonly ICartService _cartService;
        private readonly IInventoryIntegrationService _inventoryService;
        private readonly IOrderStockService _orderStockService;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly IDeliveryIntegrationService _deliveryService;
        private readonly ILogger<OrdersController> _logger;

        public OrdersController(IOrderRepository repository, ICartService cartService, IInventoryIntegrationService inventoryService, IOrderStockService orderStockService, HttpClient httpClient, IConfiguration configuration, IDeliveryIntegrationService deliveryService, ILogger<OrdersController> logger)
        {
            _repository = repository;
            _cartService = cartService;
            _inventoryService = inventoryService;
            _orderStockService = orderStockService;
            _httpClient = httpClient;
            _configuration = configuration;
            _deliveryService = deliveryService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Domain.Order>>> GetAllOrders(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentStatus = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var orders = await _repository.GetOrdersWithFiltersAsync(
                searchTerm, status, paymentStatus, startDate, endDate, pageNumber, pageSize);
            return Ok(orders);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Domain.Order>> GetOrder(int id)
        {
            var order = await _repository.GetOrderByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }
            return Ok(order);
        }

        [HttpGet("user/{userId}")]
        public async Task<ActionResult<Domain.Order>> GetOrdersByUser(string userId)
        {
            var orders = await _repository.GetOrdersByUserIdAsync(userId);
            return Ok(orders);
        }

        [HttpPost]
        public async Task<ActionResult<Domain.Order>> CreateOrder(Domain.Order order)
        {
            var createdOrder = await _repository.CreateOrderAsync(order);

            // Envoyer notification de commande créée
            await SendOrderCreatedNotificationAsync(createdOrder);

            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrder(int id, Domain.Order order)
        {
            if (id != order.Id)
            {
                return BadRequest();
            }

            await _repository.UpdateOrderAsync(order);

            return NoContent();
        }

        [HttpPost("{orderId}/cancel")]
        public async Task<ActionResult> CancelOrder(int orderId, [FromBody] CancelOrderRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            if (order.Status == Domain.OrderStatus.Paid || order.Status == Domain.OrderStatus.Shipped)
            {
                return BadRequest("Cannot cancel a paid or shipped order");
            }

            try
            {
                // Libérer les réservations de stock
                _logger.LogInformation("Libération des réservations pour la commande annulée {OrderId}", orderId);

                var cartItems = order.OrderItems.Select(oi => new DTOs.CartItemDto
                {
                    ProductId = oi.ProductId,
                    ProductName = oi.ProductName,
                    Quantity = oi.Quantity,
                    UnitPrice = oi.UnitPrice
                }).ToList();

                await _orderStockService.ReleaseStockReservationAsync(
                    orderId.ToString(),
                    order.UserId,
                    cartItems,
                    request.Reason ?? "Commande annulée");

                // Mettre à jour le statut de la commande
                order.Status = Domain.OrderStatus.Cancelled;
                await _repository.UpdateOrderAsync(order);

                _logger.LogInformation("Commande {OrderId} annulée avec succès", orderId);
                return Ok(new { message = "Order cancelled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'annulation de la commande {OrderId}", orderId);
                return StatusCode(500, new { error = "Error cancelling order", details = ex.Message });
            }
        }

        [HttpGet("{orderId}/stock-status")]
        public async Task<ActionResult> GetOrderStockStatus(int orderId)
        {
            try
            {
                var reservationStatus = await _orderStockService.GetOrderReservationStatusAsync(orderId.ToString());
                return Ok(reservationStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération du statut des stocks pour la commande {OrderId}", orderId);
                return StatusCode(500, new { error = "Error getting stock status", details = ex.Message });
            }
        }

        [HttpPost("checkout/{userId}")]
        public async Task<ActionResult<Domain.Order>> Checkout(string userId, [FromBody] CheckoutRequest request)
        {
            var cart = await _cartService.GetCartAsync(userId);
            if (cart == null || cart.Items.Count == 0)
            {
                return BadRequest("Cart is empty");
            }

            // Valider la disponibilité des stocks pour tous les produits du panier
            _logger.LogInformation("Validation des stocks pour la commande de l'utilisateur {UserId}", userId);

            var stockValidation = await _orderStockService.ValidateOrderStockAsync(cart.Items);
            if (!stockValidation.IsValid)
            {
                var errorDetails = stockValidation.Errors.Select(e =>
                    $"{e.ProductName}: {e.ErrorMessage} (Demandé: {e.RequestedQuantity}, Disponible: {e.AvailableQuantity})"
                ).ToList();

                _logger.LogWarning("Validation des stocks échouée pour l'utilisateur {UserId}: {ErrorCount} erreurs, {CriticalCount} critiques",
                    userId, stockValidation.Errors.Count, stockValidation.Errors.Count(e => e.IsCritical));

                return BadRequest(new {
                    error = stockValidation.GeneralErrorMessage ?? "Stock insuffisant pour certains produits",
                    details = errorDetails,
                    hasCriticalErrors = stockValidation.HasCriticalErrors
                });
            }

            _logger.LogInformation("Validation des stocks réussie pour l'utilisateur {UserId}", userId);

            // Calculer le total complet avec frais de livraison et taxes
            var subTotal = cart.Items.Sum(i => i.UnitPrice * i.Quantity);

            // Appliquer la réduction de coupon si présente
            var couponDiscount = request.CouponDiscount;
            var discountedSubTotal = Math.Max(0, subTotal - couponDiscount);

            // Calculer les frais de livraison via le service Delivery
            var shippingFee = 0m;
            if (request.ShippingAddress != null)
            {
                try
                {
                    var deliveryAddress = $"{request.ShippingAddress.Address}, {request.ShippingAddress.City}, {request.ShippingAddress.Country}";
                    shippingFee = await _deliveryService.CalculateDeliveryFeeAsync(deliveryAddress, discountedSubTotal);
                    _logger.LogInformation("Frais de livraison calculés: {ShippingFee} GNF pour l'adresse: {Address}", shippingFee, deliveryAddress);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erreur lors du calcul des frais de livraison, utilisation du tarif par défaut");
                    shippingFee = discountedSubTotal > 500000 ? 0 : 25000; // Fallback vers la logique par défaut
                }
            }

            var tax = discountedSubTotal * 0.18m; // 18% TVA
            var totalAmount = discountedSubTotal + shippingFee + tax;

            var order = new Domain.Order
            {
                UserId = userId,
                OrderDate = DateTime.UtcNow,
                TotalAmount = totalAmount,
                Currency = "GNF", // Franc Guinéen
                Status = Domain.OrderStatus.Pending,
                PaymentMethod = request.PaymentMethod,
                PaymentStatus = Domain.PaymentStatus.Pending,
                ShippingAddress = request.ShippingAddress != null ? new Domain.ShippingAddress
                {
                    FullName = request.ShippingAddress.FullName,
                    Address = request.ShippingAddress.Address,
                    City = request.ShippingAddress.City,
                    Country = request.ShippingAddress.Country,
                    PostalCode = request.ShippingAddress.PostalCode,
                    PhoneNumber = request.ShippingAddress.PhoneNumber
                } : null,
                OrderItems = await CreateOrderItemsWithSellerIdAsync(cart.Items)
            };

            var createdOrder = await _repository.CreateOrderAsync(order);

            // Réserver les stocks pour cette commande
            _logger.LogInformation("Réservation des stocks pour la commande {OrderId}", createdOrder.Id);
            try
            {
                var reservationSuccess = await _orderStockService.ReserveStockForOrderAsync(
                    createdOrder.Id.ToString(),
                    userId,
                    cart.Items);

                if (!reservationSuccess)
                {
                    _logger.LogWarning("Échec de la réservation des stocks pour la commande {OrderId}", createdOrder.Id);
                    // On continue quand même car la commande a été créée
                    // Les stocks seront vérifiés à nouveau lors du paiement
                }
                else
                {
                    _logger.LogInformation("Stocks réservés avec succès pour la commande {OrderId}", createdOrder.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réservation des stocks pour la commande {OrderId}", createdOrder.Id);
                // On continue car la commande a été créée
            }

            // Envoyer notification de commande créée
            await SendOrderCreatedNotificationAsync(createdOrder);

            // Enregistrer l'utilisation du coupon si un coupon a été appliqué
            if (request.CouponId.HasValue && couponDiscount > 0)
            {
                await RecordCouponUsageAsync(request.CouponId.Value, userId, createdOrder.Id.ToString(), couponDiscount);
            }

            // Créer automatiquement l'ordre de livraison si une adresse de livraison est fournie
            if (createdOrder.ShippingAddress != null)
            {
                try
                {
                    var deliveryOrder = await _deliveryService.CreateDeliveryOrderAsync(createdOrder);
                    if (deliveryOrder != null)
                    {
                        // Optionnel: Mettre à jour la commande avec le numéro de suivi
                        // createdOrder.TrackingNumber = deliveryOrder.TrackingNumber;
                        // await _repository.UpdateOrderAsync(createdOrder);
                    }
                }
                catch (Exception ex)
                {
                    // Log l'erreur mais ne pas faire échouer la commande
                    Console.WriteLine($"Erreur lors de la création de l'ordre de livraison: {ex.Message}");
                }
            }

            // Ne pas vider le panier ici - il sera vidé après un paiement réussi
            // await _cartService.ClearCartAsync(userId);

            return CreatedAtAction(nameof(GetOrder), new { id = createdOrder.Id }, createdOrder);
        }

        [HttpPost("{orderId}/initiate-payment")]
        public async Task<ActionResult> InitiatePayment(int orderId, [FromBody] InitiatePaymentRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            if (order.PaymentStatus != Domain.PaymentStatus.Pending)
            {
                return BadRequest("Payment already processed");
            }

            try
            {
                switch (order.PaymentMethod)
                {
                    case Domain.PaymentMethod.OrangeMoney:
                        // TODO: Appeler l'API Orange Money
                        return Ok(new {
                            message = "Orange Money payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/orange-money?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.Stripe:
                        // TODO: Appeler l'API Stripe
                        return Ok(new {
                            message = "Stripe payment initiated",
                            paymentUrl = $"http://localhost:8080/payment/stripe?orderId={orderId}",
                            orderId = orderId
                        });

                    case Domain.PaymentMethod.CashOnDelivery:
                        return Ok(new {
                            message = "Cash on delivery - no payment required",
                            orderId = orderId
                        });

                    default:
                        return BadRequest("Unsupported payment method");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Error initiating payment", details = ex.Message });
            }
        }

        [HttpPost("{orderId}/update-payment-status")]
        public async Task<ActionResult> UpdatePaymentStatus(int orderId, [FromBody] UpdatePaymentStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            order.PaymentStatus = request.PaymentStatus;
            order.PaymentTransactionId = request.TransactionId;

            if (request.PaymentStatus == Domain.PaymentStatus.Completed)
            {
                order.PaymentDate = DateTime.UtcNow;
                order.Status = Domain.OrderStatus.Paid;

                // Confirmer la réduction définitive des stocks
                _logger.LogInformation("Confirmation de la réduction des stocks pour la commande payée {OrderId}", orderId);
                try
                {
                    var cartItems = order.OrderItems.Select(oi => new DTOs.CartItemDto
                    {
                        ProductId = oi.ProductId,
                        ProductName = oi.ProductName,
                        Quantity = oi.Quantity,
                        UnitPrice = oi.UnitPrice
                    }).ToList();

                    var stockReductionSuccess = await _orderStockService.ConfirmStockReductionAsync(orderId.ToString(), cartItems);
                    if (!stockReductionSuccess)
                    {
                        _logger.LogWarning("Certains stocks n'ont pas pu être réduits pour la commande {OrderId}", orderId);
                        // On continue quand même car le paiement a été effectué
                    }
                    else
                    {
                        _logger.LogInformation("Stocks réduits avec succès pour la commande {OrderId}", orderId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de la confirmation de la réduction des stocks pour la commande {OrderId}", orderId);
                    // Ne pas faire échouer la mise à jour du paiement pour un problème de stock
                }

                // Vider le panier après un paiement réussi
                try
                {
                    await _cartService.ClearCartAsync(order.UserId);
                    _logger.LogInformation("Panier vidé pour l'utilisateur {UserId} après paiement réussi de la commande {OrderId}", order.UserId, orderId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors du vidage du panier pour l'utilisateur {UserId}", order.UserId);
                    // Ne pas faire échouer la mise à jour du paiement pour un problème de panier
                }
            }

            await _repository.UpdateOrderAsync(order);

            return Ok(new { message = "Payment status updated", orderId = orderId });
        }

        [HttpPut("{orderId}/status")]
        public async Task<ActionResult<UpdateOrderStatusResponse>> UpdateOrderStatus(int orderId, [FromBody] UpdateOrderStatusRequest request)
        {
            var order = await _repository.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return NotFound("Order not found");
            }

            // Convertir le statut string en enum
            if (Enum.TryParse<Domain.OrderStatus>(request.Status, out var orderStatus))
            {
                order.Status = orderStatus;
            }
            else
            {
                return BadRequest($"Invalid order status: {request.Status}");
            }

            // Mettre à jour les champs optionnels si fournis
            if (!string.IsNullOrEmpty(request.TrackingNumber))
            {
                // Ajouter le numéro de suivi si le domaine le supporte
                // Pour l'instant, nous pouvons l'ignorer ou l'ajouter comme note
            }

            await _repository.UpdateOrderAsync(order);

            var response = new UpdateOrderStatusResponse
            {
                Id = order.Id,
                Status = order.Status.ToString(),
                Message = "Order status updated successfully"
            };

            return Ok(response);
        }

        [HttpGet("seller/{sellerId}")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<Domain.Order>>> GetOrdersBySeller(
            int sellerId,
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentStatus = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var orders = await _repository.GetOrdersBySellerIdAsync(
                sellerId, searchTerm, status, paymentStatus, startDate, endDate, pageNumber, pageSize);
            return Ok(orders);
        }

        [HttpGet("seller/{sellerId}/count")]
        public async Task<ActionResult<int>> GetOrdersCountBySeller(int sellerId)
        {
            var count = await _repository.GetOrdersCountBySellerIdAsync(sellerId);
            return Ok(count);
        }

        private async Task<List<Domain.OrderItem>> CreateOrderItemsWithSellerIdAsync(List<NafaPlace.Order.API.DTOs.CartItemDto> cartItems)
        {
            var orderItems = new List<Domain.OrderItem>();

            foreach (var item in cartItems)
            {
                var sellerId = await GetProductSellerIdAsync(item.ProductId);

                orderItems.Add(new Domain.OrderItem
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    UnitPrice = item.UnitPrice,
                    Quantity = item.Quantity,
                    ImageUrl = item.ImageUrl,
                    SellerId = sellerId
                });
            }

            return orderItems;
        }

        private async Task<int> GetProductSellerIdAsync(int productId)
        {
            try
            {
                var catalogApiUrl = _configuration["ServiceUrls:CatalogApi"];
                Console.WriteLine($"[GetProductSellerIdAsync] CatalogApi URL: {catalogApiUrl}");

                if (string.IsNullOrEmpty(catalogApiUrl))
                {
                    Console.WriteLine("❌ CatalogApi URL not configured");
                    return 1; // Default seller ID
                }

                var url = $"{catalogApiUrl}/api/v1/products/{productId}";
                Console.WriteLine($"[GetProductSellerIdAsync] Calling: {url}");

                var response = await _httpClient.GetAsync(url);
                Console.WriteLine($"[GetProductSellerIdAsync] Response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[GetProductSellerIdAsync] Response content: {content}");

                    var product = JsonSerializer.Deserialize<ProductDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    var sellerId = product?.SellerId ?? 1;
                    Console.WriteLine($"[GetProductSellerIdAsync] Product SellerId: {sellerId}");
                    return sellerId;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting product seller ID: {ex.Message}");
            }

            Console.WriteLine("⚠️ Returning default SellerId = 1");
            return 1; // Default seller ID if error
        }

        private async Task RecordCouponUsageAsync(int couponId, string userId, string orderId, decimal discountAmount)
        {
            try
            {
                var couponApiUrl = _configuration.GetValue<string>("ApiSettings:CouponApiUrl") ?? "http://coupon-api";
                var requestData = new
                {
                    CouponId = couponId,
                    UserId = userId,
                    OrderId = orderId,
                    DiscountAmount = discountAmount
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{couponApiUrl}/api/coupon/record-usage", content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"✅ Coupon usage recorded successfully for coupon {couponId}");
                }
                else
                {
                    _logger.LogWarning("Failed to record coupon usage: {StatusCode}", response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording coupon usage");
            }
        }

        [HttpGet("delivery/calculate-fee")]
        public async Task<ActionResult<decimal>> CalculateDeliveryFee([FromQuery] string address, [FromQuery] decimal orderAmount, [FromQuery] decimal? weight = null)
        {
            try
            {
                var fee = await _deliveryService.CalculateDeliveryFeeAsync(address, orderAmount, weight);
                return Ok(fee);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du calcul des frais de livraison: {ex.Message}");
            }
        }

        [HttpPost("delivery/quotes")]
        public async Task<ActionResult<List<DeliveryQuoteDto>>> GetDeliveryQuotes([FromBody] DeliveryQuoteRequest request)
        {
            try
            {
                var quotes = await _deliveryService.GetDeliveryQuotesAsync(
                    request.Address,
                    request.OrderAmount,
                    request.Weight,
                    request.Volume
                );
                return Ok(quotes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de l'obtention des devis de livraison: {ex.Message}");
            }
        }

        [HttpGet("{orderId}/delivery/tracking")]
        public async Task<ActionResult<List<DeliveryTrackingDto>>> GetOrderDeliveryTracking(int orderId)
        {
            try
            {
                var order = await _repository.GetOrderByIdAsync(orderId);
                if (order == null)
                    return NotFound();

                // Assuming we store tracking number in order or have a way to get it
                // For now, we'll use order ID as tracking number
                var trackingNumber = $"NP{DateTime.UtcNow:yyyyMMdd}{orderId:D4}";
                var tracking = await _deliveryService.GetDeliveryTrackingAsync(trackingNumber);

                return Ok(tracking);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de l'obtention du suivi de livraison: {ex.Message}");
            }
        }

        [HttpGet("health")]
        public IActionResult Health()
        {
            return Ok(new { status = "healthy", service = "order-api", timestamp = DateTime.UtcNow });
        }

        // Méthode pour envoyer les notifications de commande créée
        private async Task SendOrderCreatedNotificationAsync(Domain.Order order)
        {
            try
            {
                var notificationData = new
                {
                    orderId = order.Id.ToString(),
                    userId = order.UserId,
                    amount = order.TotalAmount
                };

                var jsonContent = JsonSerializer.Serialize(notificationData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Envoyer via API Gateway
                var response = await _httpClient.PostAsync("http://api-gateway/api/notifications/test/webhooks/order-created", content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"✅ Notification envoyée pour la commande {order.Id}");
                }
                else
                {
                    Console.WriteLine($"⚠️ Échec envoi notification pour commande {order.Id}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur notification commande {order.Id}: {ex.Message}");
                // Ne pas faire échouer la commande si la notification échoue
            }
        }
    }

    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int SellerId { get; set; }
    }

    public class DeliveryQuoteRequest
    {
        public string Address { get; set; } = string.Empty;
        public decimal OrderAmount { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Volume { get; set; }
    }
}