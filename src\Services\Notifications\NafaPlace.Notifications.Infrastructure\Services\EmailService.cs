using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using NafaPlace.Notifications.Application.Interfaces;
using System.Net;
using System.Net.Mail;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IConfiguration _configuration;
    private readonly SmtpClient _smtpClient;

    public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _smtpClient = CreateSmtpClient();
    }

    private SmtpClient CreateSmtpClient()
    {
        var smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com";
        var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
        var smtpUsername = _configuration["Email:SmtpUsername"] ?? "";
        var smtpPassword = _configuration["Email:SmtpPassword"] ?? "";
        var enableSsl = bool.Parse(_configuration["Email:EnableSsl"] ?? "true");

        var client = new SmtpClient(smtpHost, smtpPort)
        {
            EnableSsl = enableSsl,
            UseDefaultCredentials = false,
            Credentials = new NetworkCredential(smtpUsername, smtpPassword)
        };

        return client;
    }

    public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            _logger.LogInformation("📧 Sending email to {To}: {Subject}", to, subject);

            var fromEmail = _configuration["Email:FromEmail"] ?? "<EMAIL>";
            var fromName = _configuration["Email:FromName"] ?? "NafaPlace";

            var mailMessage = new MailMessage
            {
                From = new MailAddress(fromEmail, fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            mailMessage.To.Add(to);

            // Vérifier si nous avons des credentials SMTP valides
            var smtpUsername = _configuration["Email:SmtpUsername"];
            var smtpPassword = _configuration["Email:SmtpPassword"];

            if (string.IsNullOrEmpty(smtpUsername) || string.IsNullOrEmpty(smtpPassword) || smtpPassword == "VOTRE_MOT_DE_PASSE_APPLICATION_ICI")
            {
                // Mode simulation si pas de credentials
                _logger.LogInformation("🔧 Development mode: Simulating email send (no SMTP credentials)");
                _logger.LogInformation("📧 Email details - To: {To}, Subject: {Subject}, Body: {Body}", to, subject, body);
                await Task.Delay(100);
                _logger.LogInformation("✅ Email simulated successfully to {To}", to);
            }
            else
            {
                // Envoyer l'email via SMTP
                await _smtpClient.SendMailAsync(mailMessage);
                _logger.LogInformation("✅ Email sent successfully to {To}", to);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send email to {To}", to);
            throw;
        }
    }

    public async Task SendEmailAsync(IEnumerable<string> to, string subject, string body, bool isHtml = true)
    {
        foreach (var recipient in to)
        {
            await SendEmailAsync(recipient, subject, body, isHtml);
        }
    }

    public async Task<bool> SendEmailWithTemplateAsync(string to, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            _logger.LogInformation("📧 Sending templated email to {To} with template {TemplateCode}", to, templateCode);
            
            // Simulation d'envoi avec template
            await Task.Delay(100);
            
            _logger.LogInformation("✅ Templated email sent successfully to {To}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated email to {To}", to);
            return false;
        }
    }

    public async Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true)
    {
        var successCount = 0;

        foreach (var recipient in recipients)
        {
            try
            {
                await SendEmailAsync(recipient, subject, body, isHtml);
                successCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Recipient}", recipient);
            }
        }

        _logger.LogInformation("📧 Bulk email sent: {SuccessCount}/{TotalCount}", successCount, recipients.Count);
        return successCount == recipients.Count;
    }

    public async Task<bool> IsEmailValidAsync(string email)
    {
        await Task.CompletedTask;
        
        // Validation basique d'email
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public void Dispose()
    {
        _smtpClient?.Dispose();
    }
}
