using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.Common.Models;
using NafaPlace.Identity.Application.DTOs;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace NafaPlace.Identity.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[SwaggerTag("Gestion des profils utilisateurs")]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;

    public UsersController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Récupère la liste des utilisateurs avec pagination (Admin seulement)
    /// </summary>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize"><PERSON>lle de page</param>
    /// <param name="searchTerm">Terme de recherche</param>
    /// <returns>Liste paginée des utilisateurs</returns>
    /// <response code="200">Liste récupérée avec succès</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(PagedResult<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [SwaggerOperation(
        Summary = "Récupère la liste des utilisateurs avec pagination (Admin seulement)",
        Description = "Retourne une liste paginée de tous les utilisateurs",
        OperationId = "Users_GetUsers",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> GetUsers([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 20, [FromQuery] string? searchTerm = null)
    {
        try
        {
            var result = await _userService.GetUsersAsync(pageNumber, pageSize, searchTerm);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère le profil de l'utilisateur connecté
    /// </summary>
    /// <returns>Profil de l'utilisateur</returns>
    /// <response code="200">Profil récupéré avec succès</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpGet("profile")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Récupère le profil de l'utilisateur connecté",
        Description = "Retourne les informations du profil de l'utilisateur authentifié",
        OperationId = "Users_GetProfile",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> GetProfile()
    {
        try
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié"));
            var profile = await _userService.GetUserProfileAsync(userId);
            return Ok(profile);
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Met à jour le profil de l'utilisateur connecté
    /// </summary>
    /// <param name="request">Informations du profil à mettre à jour</param>
    /// <returns>Profil mis à jour</returns>
    /// <response code="200">Profil mis à jour avec succès</response>
    /// <response code="400">Données invalides</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPut("profile")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Met à jour le profil de l'utilisateur connecté",
        Description = "Met à jour les informations du profil de l'utilisateur authentifié",
        OperationId = "Users_UpdateProfile",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> UpdateProfile([FromBody] UpdateUserProfileRequest request)
    {
        try
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié"));
            var profile = await _userService.UpdateUserProfileAsync(userId, request);
            return Ok(profile);
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Change le mot de passe de l'utilisateur connecté
    /// </summary>
    /// <param name="request">Informations pour le changement de mot de passe</param>
    /// <returns>Confirmation du changement</returns>
    /// <response code="200">Mot de passe changé avec succès</response>
    /// <response code="400">Données invalides</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPost("change-password")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Change le mot de passe de l'utilisateur connecté",
        Description = "Change le mot de passe de l'utilisateur authentifié",
        OperationId = "Users_ChangePassword",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié"));
            await _userService.ChangePasswordAsync(userId, request);
            return Ok(new { Message = "Mot de passe changé avec succès" });
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère le profil d'un utilisateur par son ID (Admin seulement)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <returns>Profil de l'utilisateur</returns>
    /// <response code="200">Profil récupéré avec succès</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpGet("{userId}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Récupère le profil d'un utilisateur par son ID (Admin seulement)",
        Description = "Retourne les informations du profil d'un utilisateur spécifié par son ID",
        OperationId = "Users_GetUserById",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> GetUserById(int userId)
    {
        try
        {
            var profile = await _userService.GetUserProfileAsync(userId);
            return Ok(profile);
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère les informations de base d'un utilisateur par son ID (pour les services internes)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <returns>Informations de base de l'utilisateur</returns>
    /// <response code="200">Informations récupérées avec succès</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpGet("internal/{userId}")]
    [AllowAnonymous] // Endpoint public pour les services internes
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Récupère les informations de base d'un utilisateur par son ID (pour les services internes)",
        Description = "Retourne les informations de base d'un utilisateur pour les autres services",
        OperationId = "Users_GetUserByIdInternal",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> GetUserByIdInternal(int userId)
    {
        try
        {
            var profile = await _userService.GetUserProfileAsync(userId);
            return Ok(profile);
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Met à jour un utilisateur (Admin seulement)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="request">Données de mise à jour</param>
    /// <returns>Utilisateur mis à jour</returns>
    /// <response code="200">Utilisateur mis à jour avec succès</response>
    /// <response code="400">Données invalides</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPut("{userId}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Met à jour un utilisateur (Admin seulement)",
        Description = "Met à jour les informations d'un utilisateur spécifique",
        OperationId = "Users_UpdateUser",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> UpdateUser(int userId, [FromBody] UpdateUserRequest request)
    {
        try
        {
            var user = await _userService.UpdateUserAsync(userId, request);
            return Ok(user);
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Met à jour les rôles d'un utilisateur (Admin seulement)
    /// </summary>
    /// <param name="userId">ID de l'utilisateur</param>
    /// <param name="request">Nouveaux rôles à assigner</param>
    /// <returns>Confirmation de la mise à jour</returns>
    /// <response code="200">Rôles mis à jour avec succès</response>
    /// <response code="400">Données invalides</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPut("{userId}/roles")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Met à jour les rôles d'un utilisateur (Admin seulement)",
        Description = "Remplace tous les rôles d'un utilisateur par les nouveaux rôles spécifiés",
        OperationId = "Users_UpdateRoles",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> UpdateUserRoles(int userId, [FromBody] UpdateUserRolesRequest request)
    {
        try
        {
            await _userService.UpdateUserRolesAsync(userId, request.Roles);
            return Ok(new { success = true, message = "Rôles mis à jour avec succès" });
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Met à jour les rôles d'un utilisateur via POST (Admin seulement) - Endpoint alternatif
    /// </summary>
    /// <param name="request">Données de mise à jour des rôles</param>
    /// <returns>Confirmation de la mise à jour</returns>
    /// <response code="200">Rôles mis à jour avec succès</response>
    /// <response code="400">Données invalides</response>
    /// <response code="401">Non authentifié</response>
    /// <response code="403">Non autorisé</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPost("update-roles")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Met à jour les rôles d'un utilisateur via POST (Admin seulement)",
        Description = "Endpoint alternatif pour mettre à jour les rôles d'un utilisateur",
        OperationId = "Users_UpdateRolesPost",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> UpdateUserRolesPost([FromBody] UpdateUserRolesPostRequest request)
    {
        try
        {
            await _userService.UpdateUserRolesAsync(request.UserId, request.Roles);
            return Ok(new { success = true, message = "Rôles mis à jour avec succès" });
        }
        catch (AuthenticationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }

    /// <summary>
    /// Initie la réinitialisation du mot de passe
    /// </summary>
    /// <param name="request">Demande de réinitialisation</param>
    /// <returns>Confirmation de l'envoi</returns>
    /// <response code="200">Email de réinitialisation envoyé</response>
    /// <response code="404">Utilisateur non trouvé</response>
    [HttpPost("forgot-password")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [SwaggerOperation(
        Summary = "Initie la réinitialisation du mot de passe",
        Description = "Envoie un email avec un lien de réinitialisation du mot de passe",
        OperationId = "Users_ForgotPassword",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        try
        {
            await _userService.InitiatePasswordResetAsync(request.Email);
            return Ok(new { Message = "Si cette adresse email existe, un lien de réinitialisation a été envoyé." });
        }
        catch (Exception ex)
        {
            // Ne pas révéler si l'email existe ou non pour des raisons de sécurité
            return Ok(new { Message = "Si cette adresse email existe, un lien de réinitialisation a été envoyé." });
        }
    }

    /// <summary>
    /// Réinitialise le mot de passe avec un token
    /// </summary>
    /// <param name="request">Demande de réinitialisation avec token</param>
    /// <returns>Confirmation de la réinitialisation</returns>
    /// <response code="200">Mot de passe réinitialisé avec succès</response>
    /// <response code="400">Token invalide ou expiré</response>
    [HttpPost("reset-password")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [SwaggerOperation(
        Summary = "Réinitialise le mot de passe avec un token",
        Description = "Réinitialise le mot de passe en utilisant le token reçu par email",
        OperationId = "Users_ResetPassword",
        Tags = new[] { "Users" }
    )]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        try
        {
            await _userService.ResetPasswordAsync(request);
            return Ok(new { Message = "Mot de passe réinitialisé avec succès" });
        }
        catch (AuthenticationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Une erreur s'est produite: {ex.Message}");
        }
    }
}
