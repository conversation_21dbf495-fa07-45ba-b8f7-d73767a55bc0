{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=5434;Database=NafaPlace.Order;User Id=postgres;Password=*****************"}, "AllowedHosts": "*", "ServiceUrls": {"CartApi": "http://cart-api", "CatalogApi": "http://catalog-api"}, "DeliveryApiUrl": "http://delivery-api", "JwtSettings": {"Secret": "NafaPlaceSecretKey2025ForProductionEnvironment", "Issuer": "NafaPlace", "Audience": "NafaPlaceApi", "TokenLifetimeMinutes": 60}}