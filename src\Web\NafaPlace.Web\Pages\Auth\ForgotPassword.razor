@page "/auth/forgot-password"
@layout EmptyLayout
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<PageTitle>Mot de passe oublié - NafaPlace</PageTitle>

<div class="auth-page forgot-password-page">
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow form-container">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <img src="/images/nafaplace-gradient-modern.svg" alt="NafaPlace" height="60" class="mb-3">
                            <h3 class="fw-bold">Mot de passe oublié</h3>
                            <p class="text-muted">Entrez votre email pour recevoir un lien de réinitialisation</p>
                        </div>

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>@errorMessage
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle-fill me-2"></i>@successMessage
                            </div>
                        }

                        @if (!emailSent)
                        {
                            <EditForm Model="@forgotPasswordRequest" OnValidSubmit="HandleForgotPasswordSubmit">
                                <DataAnnotationsValidator />

                                <div class="mb-3">
                                    <label for="email" class="form-label">Adresse email</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                        <InputText type="email" class="form-control" id="email" placeholder="<EMAIL>" @bind-Value="forgotPasswordRequest.Email" />
                                    </div>
                                    <ValidationMessage For="@(() => forgotPasswordRequest.Email)" />
                                </div>

                                <div class="d-grid gap-2 mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                        @if (isLoading)
                                        {
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span class="ms-2">Envoi en cours...</span>
                                        }
                                        else
                                        {
                                            <span><i class="bi bi-envelope me-2"></i>Envoyer le lien</span>
                                        }
                                    </button>
                                </div>
                            </EditForm>
                        }

                        <div class="text-center">
                            <p class="small text-muted mb-2">Vous vous souvenez de votre mot de passe ?</p>
                            <a href="/auth/login" class="text-decoration-none">
                                <i class="bi bi-arrow-left me-1"></i>Retour à la connexion
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ForgotPasswordRequest forgotPasswordRequest = new ForgotPasswordRequest();
    private bool isLoading = false;
    private bool emailSent = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    private async Task HandleForgotPasswordSubmit()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            // Appeler l'API pour initier la réinitialisation
            var response = await AuthService.ForgotPasswordAsync(forgotPasswordRequest);

            if (response.Success)
            {
                emailSent = true;
                successMessage = "Si cette adresse email existe, un lien de réinitialisation a été envoyé. Vérifiez votre boîte de réception.";
            }
            else
            {
                errorMessage = response.Message ?? "Une erreur s'est produite. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
