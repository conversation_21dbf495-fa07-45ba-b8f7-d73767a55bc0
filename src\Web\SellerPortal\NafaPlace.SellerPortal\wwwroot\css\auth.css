/* Styles spécifiques pour les pages d'authentification - Seller Portal */

.auth-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.form-container {
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.form-container .card-body {
    padding: 3rem 2.5rem;
}

.form-container h3, .form-container h4 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-container .text-muted {
    color: #6c757d !important;
    font-size: 0.95rem;
}

.form-container .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-container .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-container .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-container .input-group-text {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 10px 0 0 10px;
    color: #6c757d;
}

.form-container .input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

.form-container .input-group:focus-within .input-group-text {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

.form-container .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-container .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.form-container .btn-primary:disabled {
    opacity: 0.7;
    transform: none;
    box-shadow: none;
}

.form-container .alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.form-container .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-left: 4px solid #dc3545;
}

.form-container .alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border-left: 4px solid #198754;
}

.form-container a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.form-container a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .auth-page {
        padding: 1rem;
    }
    
    .form-container .card-body {
        padding: 2rem 1.5rem;
    }
}

/* Animation pour les éléments */
.form-container {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Style pour les messages de validation */
.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Style pour le spinner de chargement */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Style spécifique pour le seller portal */
.seller-portal-icon {
    color: #28a745;
    font-size: 2.5rem;
}

.seller-portal-title {
    color: #28a745;
    font-weight: 700;
}
