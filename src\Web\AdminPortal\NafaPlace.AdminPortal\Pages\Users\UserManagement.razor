@page "/users"
@attribute [AdminOnly]
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.AdminPortal.Models.Auth
@using UserModels = NafaPlace.AdminPortal.Models.Users
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Routing
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject IUserService UserService
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Admin")]

<PageTitle>Gestion des Utilisateurs - NafaPlace Admin</PageTitle>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Utilisateurs</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Tableau de bord</a></li>
        <li class="breadcrumb-item active">Utilisateurs</li>
    </ol>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @errorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @successMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">Liste des Utilisateurs</h5>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                                <i class="fas fa-plus"></i> Nouvel Utilisateur
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Barre de recherche -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Rechercher par nom, email..." 
                                       @bind="searchTerm" @onkeypress="OnSearchKeyPress" />
                                <button class="btn btn-outline-secondary" type="button" @onclick="SearchUsers">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <select class="form-select w-auto" value="@pageSize" @onchange="OnPageSizeChanged">
                                    <option value="10">10 par page</option>
                                    <option value="20">20 par page</option>
                                    <option value="50">50 par page</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="d-flex justify-content-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (users == null || !users.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun utilisateur trouvé.</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nom complet</th>
                                        <th>Email</th>
                                        <th>Rôles</th>
                                        <th>Statut</th>
                                        <th>Date de création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in users)
                                    {
                                        <tr>
                                            <td>@user.Id</td>
                                            <td>@user.FullName</td>
                                            <td>@user.Email</td>
                                            <td>
                                                @if (user.Roles.Any())
                                                {
                                                    @foreach (var role in user.Roles)
                                                    {
                                                        <span class="badge bg-primary me-1">@role</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Aucun rôle</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">Actif</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactif</span>
                                                }
                                            </td>
                                            <td>@user.CreatedAt.ToShortDateString()</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditUser(user)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ManageUserRoles(user)">
                                                        <i class="fas fa-user-tag"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteUserConfirmation(user)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (totalPages > 1)
                        {
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" href="javascript:void(0)" @onclick="PreviousPage">Précédent</a>
                                    </li>
                                    @for (int i = 1; i <= totalPages; i++)
                                    {
                                        var pageNumber = i;
                                        <li class="page-item @(pageNumber == currentPage ? "active" : "")">
                                            <a class="page-link" href="javascript:void(0)" @onclick="() => GoToPage(pageNumber)">@pageNumber</a>
                                        </li>
                                    }
                                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                        <a class="page-link" href="javascript:void(0)" @onclick="NextPage">Suivant</a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de création d'utilisateur -->
@if (showCreateModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Créer un nouvel utilisateur</h5>
                    <button type="button" class="btn-close" @onclick="CloseModals"></button>
                </div>
                <EditForm Model="createUserRequest" OnValidSubmit="CreateUser">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <InputText @bind-Value="createUserRequest.Email" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.Email)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <InputText @bind-Value="createUserRequest.Email" class="form-control" type="email" />
                                    <ValidationMessage For="@(() => createUserRequest.Email)" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <InputText @bind-Value="createUserRequest.FirstName" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.FirstName)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom de famille *</label>
                                    <InputText @bind-Value="createUserRequest.LastName" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.LastName)" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <InputText @bind-Value="createUserRequest.PhoneNumber" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.PhoneNumber)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <InputCheckbox @bind-Value="createUserRequest.IsActive" />
                                        Utilisateur actif
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mot de passe *</label>
                                    <InputText @bind-Value="createUserRequest.Password" type="password" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.Password)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Confirmer le mot de passe *</label>
                                    <InputText @bind-Value="createUserRequest.ConfirmPassword" type="password" class="form-control" />
                                    <ValidationMessage For="@(() => createUserRequest.ConfirmPassword)" />
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Rôles</label>
                            @foreach (var role in availableRoles)
                            {
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           checked="@createUserRequest.Roles.Contains(role.Name)"
                                           @onchange="@((e) => ToggleRole(createUserRequest.Roles, role.Name, (bool)e.Value!))" />
                                    <label class="form-check-label">@role.Name</label>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModals">Annuler</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Créer
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<!-- Modal d'édition d'utilisateur -->
@if (showEditModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Modifier l'utilisateur</h5>
                    <button type="button" class="btn-close" @onclick="CloseModals"></button>
                </div>
                <EditForm Model="updateUserRequest" OnValidSubmit="UpdateUser">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <InputText @bind-Value="updateUserRequest.Email" class="form-control" />
                                    <ValidationMessage For="@(() => updateUserRequest.Email)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <InputText @bind-Value="updateUserRequest.PhoneNumber" class="form-control" />
                                    <ValidationMessage For="@(() => updateUserRequest.PhoneNumber)" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <InputText @bind-Value="updateUserRequest.FirstName" class="form-control" />
                                    <ValidationMessage For="@(() => updateUserRequest.FirstName)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom de famille *</label>
                                    <InputText @bind-Value="updateUserRequest.LastName" class="form-control" />
                                    <ValidationMessage For="@(() => updateUserRequest.LastName)" />
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">
                                <InputCheckbox @bind-Value="updateUserRequest.IsActive" />
                                Utilisateur actif
                            </label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Rôles</label>
                            @foreach (var role in availableRoles)
                            {
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           checked="@updateUserRequest.Roles.Contains(role.Name)"
                                           @onchange="@((e) => ToggleRole(updateUserRequest.Roles, role.Name, (bool)e.Value!))" />
                                    <label class="form-check-label">@role.Name</label>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModals">Annuler</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Mettre à jour
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<!-- Modal de gestion des rôles -->
@if (showRolesModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Gérer les rôles de @selectedUser.FullName</h5>
                    <button type="button" class="btn-close" @onclick="CloseModals"></button>
                </div>
                <div class="modal-body">
                    @foreach (var role in availableRoles)
                    {
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                   checked="@selectedRoles.Contains(role.Name)"
                                   @onchange="@((e) => ToggleRole(selectedRoles, role.Name, (bool)e.Value!))" />
                            <label class="form-check-label">@role.Name</label>
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseModals">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="UpdateUserRoles" disabled="@isSubmitting">
                        @if (isSubmitting)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Mettre à jour les rôles
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Modal de confirmation de suppression -->
@if (showDeleteModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" @onclick="CloseModals"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>@selectedUser.FullName</strong> ?</p>
                    <p class="text-danger">Cette action est irréversible.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseModals">Annuler</button>
                    <button type="button" class="btn btn-danger" @onclick="DeleteUser" disabled="@isSubmitting">
                        @if (isSubmitting)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<UserModels.UserDto> users = new List<UserModels.UserDto>();
    private List<UserModels.RoleDto> availableRoles = new List<UserModels.RoleDto>();
    private bool isLoading = true;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private string searchTerm = string.Empty;
    private int currentPage = 1;
    private int pageSize = 20;
    private int totalPages = 1;
    private int totalCount = 0;

    // Variables pour les modals
    private bool showCreateModal = false;
    private bool showEditModal = false;
    private bool showRolesModal = false;
    private bool showDeleteModal = false;

    // Variables pour les formulaires
    private UserModels.CreateUserRequest createUserRequest = new();
    private UserModels.UpdateUserRequest updateUserRequest = new();
    private UserModels.UserDto selectedUser = new();
    private List<string> selectedRoles = new();
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        await LoadRoles();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            
            var result = await UserService.GetUsersAsync(currentPage, pageSize, searchTerm);
            users = result.Items.ToList();
            totalCount = result.TotalCount;
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement des utilisateurs: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchUsers()
    {
        currentPage = 1;
        await LoadUsers();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchUsers();
        }
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var newPageSize))
        {
            pageSize = newPageSize;
            currentPage = 1;
            await LoadUsers();
        }
    }

    private async Task GoToPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadUsers();
        }
    }

    private async Task PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            await LoadUsers();
        }
    }

    private async Task NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            await LoadUsers();
        }
    }

    private async Task LoadRoles()
    {
        try
        {
            availableRoles = await UserService.GetRolesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des rôles: {ex.Message}");
        }
    }

    private void ShowCreateUserModal()
    {
        createUserRequest = new UserModels.CreateUserRequest();
        showCreateModal = true;
    }

    private async Task CreateUser()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            var newUser = await UserService.CreateUserAsync(createUserRequest);
            successMessage = $"Utilisateur {newUser.FullName} créé avec succès";
            showCreateModal = false;
            await LoadUsers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la création de l'utilisateur: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void EditUser(UserModels.UserDto user)
    {
        selectedUser = user;
        updateUserRequest = new UserModels.UpdateUserRequest
        {
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            PhoneNumber = user.PhoneNumber,
            Roles = user.Roles.ToList(),
            IsActive = user.IsActive
        };
        showEditModal = true;
    }

    private async Task UpdateUser()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            var updatedUser = await UserService.UpdateUserAsync(selectedUser.Id, updateUserRequest);
            successMessage = $"Utilisateur {updatedUser.FullName} mis à jour avec succès";
            showEditModal = false;
            await LoadUsers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la mise à jour de l'utilisateur: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void ManageUserRoles(UserModels.UserDto user)
    {
        selectedUser = user;
        selectedRoles = user.Roles.ToList();
        showRolesModal = true;
    }

    private async Task UpdateUserRoles()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            await UserService.UpdateUserRolesAsync(selectedUser.Id, selectedRoles);
            successMessage = $"Rôles de {selectedUser.FullName} mis à jour avec succès";
            showRolesModal = false;
            await LoadUsers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la mise à jour des rôles: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void DeleteUserConfirmation(UserModels.UserDto user)
    {
        selectedUser = user;
        showDeleteModal = true;
    }

    private async Task DeleteUser()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            await UserService.DeleteUserAsync(selectedUser.Id);
            successMessage = $"Utilisateur {selectedUser.FullName} supprimé avec succès";
            showDeleteModal = false;
            await LoadUsers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la suppression de l'utilisateur: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void CloseModals()
    {
        showCreateModal = false;
        showEditModal = false;
        showRolesModal = false;
        showDeleteModal = false;
        isSubmitting = false;
    }

    private void ToggleRole(List<string> rolesList, string roleName, bool isChecked)
    {
        if (isChecked && !rolesList.Contains(roleName))
        {
            rolesList.Add(roleName);
        }
        else if (!isChecked && rolesList.Contains(roleName))
        {
            rolesList.Remove(roleName);
        }
    }
}
