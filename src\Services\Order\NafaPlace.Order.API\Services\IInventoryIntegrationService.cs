using NafaPlace.Order.API.DTOs;

namespace NafaPlace.Order.API.Services
{
    public interface IInventoryIntegrationService
    {
        Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity);
        Task<bool> ValidateCartStockAsync(List<CartItemDto> cartItems);
        Task<bool> ReduceStockAsync(int productId, int quantity, string reason, string orderId);
        Task<bool> ReduceStockForOrderAsync(List<CartItemDto> orderItems, string orderId);
        Task<bool> ReserveStockAsync(int productId, int quantity, string userId, string? sessionId = null);
        Task<bool> ReleaseReservationAsync(int productId, int quantity, string userId, string reason);
        Task<int> GetAvailableStockAsync(int productId);
    }

    public class StockValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public int AvailableStock { get; set; }
        public int ReservedStock { get; set; }
        public int RequestedQuantity { get; set; }
        public bool RequiresReservation { get; set; }
    }
}
