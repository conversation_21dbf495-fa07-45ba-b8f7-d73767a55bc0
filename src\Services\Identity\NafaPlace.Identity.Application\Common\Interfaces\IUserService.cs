using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Application.Common.Models;
using System;

namespace NafaPlace.Identity.Application.Common.Interfaces;

public interface IUserService
{
    Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null);
    Task<UserDto> GetUserProfileAsync(int userId);
    Task<UserDto> UpdateUserProfileAsync(int userId, UpdateUserProfileRequest request);
    Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request);
    Task ChangePasswordAsync(int userId, ChangePasswordRequest request);
    Task UpdateUserRolesAsync(int userId, List<string> roles);
    Task InitiatePasswordResetAsync(string email);
    Task ResetPasswordAsync(ResetPasswordRequest request);
}
