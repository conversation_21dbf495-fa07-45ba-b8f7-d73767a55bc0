using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using NafaPlace.SellerPortal;
using NafaPlace.SellerPortal.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authorization;
using Blazored.LocalStorage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.JSInterop;
using NafaPlace.Common.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Récupération des paramètres de configuration
var identityApiUrl = builder.Configuration.GetValue<string>("ApiSettings:IdentityApiUrl") ?? "http://localhost:5155";
var catalogApiUrl = builder.Configuration.GetValue<string>("ApiSettings:CatalogApiUrl") ?? "http://localhost:5243";
var reviewApiUrl = builder.Configuration.GetValue<string>("ApiSettings:ReviewApiUrl") ?? "http://localhost:5006";
var inventoryApiUrl = builder.Configuration.GetValue<string>("ApiSettings:InventoryApiUrl") ?? "http://localhost:5244";
var orderApiUrl = builder.Configuration.GetValue<string>("ApiSettings:OrderApiUrl") ?? "http://localhost:5004";

// Ajout du service LocalStorage
builder.Services.AddBlazoredLocalStorage();

// Ajout de HttpClient et HttpClientFactory
builder.Services.AddHttpClient();
builder.Services.AddScoped<HttpClient>(sp => new HttpClient());

// Configuration du client nommé pour l'API Identity
builder.Services.AddHttpClient("IdentityApi", client =>
{
    client.BaseAddress = new Uri("http://localhost:5000/");
});

// Configuration du client nommé pour l'API Catalog
builder.Services.AddHttpClient("CatalogApi", client =>
{
    client.BaseAddress = new Uri("http://localhost:5000/");
});

// Configuration du client nommé pour l'API Orders
builder.Services.AddHttpClient("OrderApi", client =>
{
    client.BaseAddress = new Uri("http://localhost:5000/");
});

// Configuration du client nommé pour l'API Inventory
builder.Services.AddHttpClient("InventoryApi", client =>
{
    client.BaseAddress = new Uri("http://localhost:5000/");
});

// Configuration du service Gateway centralisé
builder.Services.AddScoped<IGatewayHttpClientService, BlazorWebAssemblyGatewayHttpClientService>();

// Services utilisant la Gateway (via HttpClient)
builder.Services.AddScoped<ProductService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var logger = sp.GetRequiredService<ILogger<ProductService>>();
    var configuration = sp.GetRequiredService<IConfiguration>();
    return new ProductService(httpClientFactory, logger, configuration);
});

builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<ImageService>();

// Services pour les commandes et statistiques (via Gateway)
builder.Services.AddScoped<IOrderService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    var configuration = sp.GetRequiredService<IConfiguration>();
    return new OrderService(httpClientFactory, jsRuntime, configuration);
});

builder.Services.AddScoped<IStatisticsService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    var configuration = sp.GetRequiredService<IConfiguration>();
    return new StatisticsService(httpClientFactory, jsRuntime, configuration);
});

// Service pour les reviews (via Gateway)
builder.Services.AddScoped<IReviewService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new ReviewService(httpClientFactory, localStorage);
});

// Service pour l'inventaire (via Gateway)
builder.Services.AddScoped<InventoryService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    return new InventoryService(httpClientFactory);
});

// Service pour les coupons (via Gateway)
builder.Services.AddScoped<CouponService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri("http://localhost:5000/") };
    var configuration = sp.GetRequiredService<IConfiguration>();
    var logger = sp.GetRequiredService<ILogger<CouponService>>();
    return new CouponService(httpClient, configuration, logger);
});

// Services d'authentification
builder.Services.AddScoped<AuthenticationStateProvider, ApiAuthenticationStateProvider>();
builder.Services.AddScoped<IAuthService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    return new AuthService(httpClientFactory, localStorage, authStateProvider);
});
builder.Services.AddAuthorizationCore(options =>
{
    // Politique pour les vendeurs uniquement
    options.AddPolicy("SellerOnly", policy =>
        policy.RequireRole("Seller"));

    // Politique par défaut : exiger l'authentification et le rôle Seller
    options.DefaultPolicy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .RequireRole("Seller")
        .Build();
});
builder.Services.AddSingleton<TokenExpirationService>();

Console.WriteLine($"Identity API URL: {identityApiUrl}");
Console.WriteLine($"Catalog API URL: {catalogApiUrl}");

var app = builder.Build();

// Démarrer le service de vérification d'expiration des tokens
var tokenExpirationService = app.Services.GetRequiredService<TokenExpirationService>();
tokenExpirationService.StartTokenExpirationCheck();

await app.RunAsync();
