using System.Text.Json;
using NafaPlace.Order.API.DTOs;

namespace NafaPlace.Order.API.Services
{
    public class InventoryIntegrationService : IInventoryIntegrationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<InventoryIntegrationService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public InventoryIntegrationService(HttpClient httpClient, ILogger<InventoryIntegrationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/inventory/products/{productId}/validate?quantity={quantity}");

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<StockValidationResult>(json, _jsonOptions);
                    return result ?? new StockValidationResult { IsValid = false, ErrorMessage = "Erreur de désérialisation" };
                }
                else
                {
                    _logger.LogWarning("Échec de validation du stock pour le produit {ProductId}: {StatusCode}", productId, response.StatusCode);
                    return new StockValidationResult { IsValid = false, ErrorMessage = "Erreur de validation du stock" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la validation du stock pour le produit {ProductId}", productId);
                return new StockValidationResult { IsValid = false, ErrorMessage = "Erreur de communication avec le service d'inventaire" };
            }
        }

        public async Task<bool> ValidateCartStockAsync(List<CartItemDto> cartItems)
        {
            try
            {
                foreach (var item in cartItems)
                {
                    var validation = await ValidateStockAvailabilityAsync(item.ProductId, item.Quantity);
                    if (!validation.IsValid)
                    {
                        _logger.LogWarning("Stock insuffisant pour le produit {ProductId}: demandé {Requested}, disponible {Available}", 
                            item.ProductId, item.Quantity, validation.AvailableStock);
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la validation du stock du panier");
                return false;
            }
        }

        public async Task<bool> ReduceStockAsync(int productId, int quantity, string reason, string orderId)
        {
            try
            {
                // D'abord, obtenir le stock actuel
                var currentStock = await GetAvailableStockAsync(productId);
                if (currentStock < quantity)
                {
                    _logger.LogWarning("Stock insuffisant pour réduire le produit {ProductId}: actuel {Current}, demandé {Requested}", 
                        productId, currentStock, quantity);
                    return false;
                }

                // Calculer le nouveau stock
                var newStock = currentStock - quantity;

                // Mettre à jour le stock via l'API Inventory
                var updateRequest = new
                {
                    NewQuantity = newStock,
                    Reason = reason,
                    Notes = $"Réduction automatique pour commande {orderId}"
                };

                var response = await _httpClient.PutAsJsonAsync($"/api/inventory/products/{productId}/stock", updateRequest);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Stock réduit avec succès pour le produit {ProductId}: {OldStock} -> {NewStock}", 
                        productId, currentStock, newStock);
                    return true;
                }
                else
                {
                    _logger.LogError("Échec de la réduction du stock pour le produit {ProductId}: {StatusCode}", 
                        productId, response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réduction du stock pour le produit {ProductId}", productId);
                return false;
            }
        }

        public async Task<bool> ReduceStockForOrderAsync(List<CartItemDto> orderItems, string orderId)
        {
            try
            {
                var allSuccessful = true;
                var processedItems = new List<(int ProductId, int Quantity, int OldStock)>();

                foreach (var item in orderItems)
                {
                    var success = await ReduceStockAsync(item.ProductId, item.Quantity, "Vente", orderId);
                    if (success)
                    {
                        processedItems.Add((item.ProductId, item.Quantity, 0)); // On stocke pour un éventuel rollback
                    }
                    else
                    {
                        allSuccessful = false;
                        _logger.LogError("Échec de la réduction du stock pour le produit {ProductId} dans la commande {OrderId}", 
                            item.ProductId, orderId);
                        
                        // En cas d'échec, on pourrait implémenter un rollback ici
                        // Pour l'instant, on continue avec les autres produits
                    }
                }

                if (allSuccessful)
                {
                    _logger.LogInformation("Tous les stocks ont été réduits avec succès pour la commande {OrderId}", orderId);
                }
                else
                {
                    _logger.LogWarning("Certains stocks n'ont pas pu être réduits pour la commande {OrderId}", orderId);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réduction des stocks pour la commande {OrderId}", orderId);
                return false;
            }
        }

        public async Task<bool> ReserveStockAsync(int productId, int quantity, string userId, string? sessionId = null)
        {
            try
            {
                var reservationRequest = new
                {
                    Quantity = quantity,
                    UserId = userId,
                    SessionId = sessionId,
                    ExpirationMinutes = 30,
                    Reason = "Réservation pour commande"
                };

                var response = await _httpClient.PostAsJsonAsync($"/api/inventory/products/{productId}/reserve", reservationRequest);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réservation du stock pour le produit {ProductId}", productId);
                return false;
            }
        }

        public async Task<bool> ReleaseReservationAsync(int productId, int quantity, string userId, string reason)
        {
            try
            {
                var releaseRequest = new
                {
                    Quantity = quantity,
                    UserId = userId,
                    Reason = reason
                };

                var response = await _httpClient.PostAsJsonAsync($"/api/inventory/products/{productId}/release", releaseRequest);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la libération de la réservation pour le produit {ProductId}", productId);
                return false;
            }
        }

        public async Task<int> GetAvailableStockAsync(int productId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/inventory/products/{productId}/stock");
                
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    if (int.TryParse(json, out int stock))
                    {
                        return stock;
                    }
                }
                
                _logger.LogWarning("Impossible d'obtenir le stock pour le produit {ProductId}", productId);
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération du stock pour le produit {ProductId}", productId);
                return 0;
            }
        }
    }
}
